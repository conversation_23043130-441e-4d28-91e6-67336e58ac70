version: '3.8'

services:
  postgres:
    image: postgres:13
    container_name: github-secrets-db
    environment:
      POSTGRES_DB: github_secrets
      POSTGRES_USER: secrets_user
      POSTGRES_PASSWORD: secrets_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U secrets_user -d github_secrets"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: github-secrets-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DATABASE_URL: **********************************************
      DATABASE_USERNAME: secrets_user
      DATABASE_PASSWORD: secrets_pass
      GITHUB_TOKEN: ${GITHUB_TOKEN}
      JWT_SECRET: ${JWT_SECRET:-default-jwt-secret-change-in-production}
      SCAN_INTERVAL: ${SCAN_INTERVAL:-15}
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: github-secrets-frontend
    environment:
      REACT_APP_API_URL: http://localhost:8080
      REACT_APP_WS_URL: ws://localhost:8080
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  default:
    name: github-secrets-network
