# GitHub Secret Scanner

A full-stack web application for real-time monitoring of GitHub repositories to detect potentially exposed API keys and secrets.

## Architecture

- **Backend**: Java Spring Boot REST API with scheduled scanning jobs
- **Frontend**: React.js dashboard for viewing alerts and managing repositories
- **Database**: PostgreSQL for storing scan results, repository configurations, and alert history
- **Real-time updates**: WebSocket for live notifications

## Features

### Repository Monitoring
- Support for both personal and public repositories
- Web interface for repository configuration
- Add repositories by URL, username, or organization

### Secret Detection
- AWS credentials (AKIA*, ASIA*, 40-char secret keys)
- Google API keys (AIza* format)
- GitHub tokens (ghp_*, gho_*, ghu_*, github_pat_*)
- Generic API patterns (api_key=, secret=, token=, password=)
- Base64 encoded secrets with entropy analysis
- JWT tokens (eyJ* format)
- Database connection strings
- Private keys (-----BEGIN PRIVATE KEY-----)

### Web Interface
- Real-time dashboard with alerts and statistics
- Repository management
- Alert details with file context
- Settings and configuration
- Export functionality for compliance reports

### Security Features
- Encrypted storage of GitHub access tokens
- No storage of actual secret values (only metadata)
- Proper authentication and authorization
- Rate limiting and abuse prevention

## Quick Start

### Prerequisites
- Java 17+
- Node.js 18+
- PostgreSQL 13+
- Docker (optional)

### Development Setup

1. **Backend Setup**
   ```bash
   cd backend
   ./mvnw spring-boot:run
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. **Database Setup**
   ```bash
   docker run --name postgres-secrets \
     -e POSTGRES_DB=github_secrets \
     -e POSTGRES_USER=secrets_user \
     -e POSTGRES_PASSWORD=secrets_pass \
     -p 5432:5432 -d postgres:13
   ```

### Docker Deployment

```bash
docker-compose up -d
```

## Configuration

### Environment Variables

- `GITHUB_TOKEN`: GitHub personal access token
- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret for JWT token signing
- `SCAN_INTERVAL`: Scanning interval in minutes (default: 15)

## API Documentation

API documentation is available at `/swagger-ui.html` when running the backend.

## Security Considerations

- Never store actual secret values
- Encrypt sensitive configuration data
- Implement proper rate limiting
- Use secure authentication mechanisms
- Regular security audits and updates

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
