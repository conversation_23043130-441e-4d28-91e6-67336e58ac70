# GitHub Configuration
GITHUB_TOKEN=your_github_personal_access_token_here

# Database Configuration
DATABASE_URL=***********************************************
DATABASE_USERNAME=secrets_user
DATABASE_PASSWORD=secrets_pass

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Application Configuration
SCAN_INTERVAL=15
LOG_LEVEL=INFO

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8080
REACT_APP_WS_URL=ws://localhost:8080
