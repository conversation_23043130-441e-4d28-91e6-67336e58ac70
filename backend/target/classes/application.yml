spring:
  application:
    name: github-secrets-scanner
  
  datasource:
    url: ${DATABASE_URL:***********************************************}
    username: ${DATABASE_USERNAME:secrets_user}
    password: ${DATABASE_PASSWORD:secrets_pass}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
  
  security:
    oauth2:
      client:
        registration:
          github:
            client-id: ${GITHUB_CLIENT_ID:}
            client-secret: ${GITHUB_CLIENT_SECRET:}
  
  mail:
    host: ${SMTP_HOST:smtp.gmail.com}
    port: ${SMTP_PORT:587}
    username: ${SMTP_USERNAME:}
    password: ${SMTP_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

server:
  port: 8080
  servlet:
    context-path: /api

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.github.secrets: ${LOG_LEVEL:INFO}
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

app:
  github:
    token: ${GITHUB_TOKEN:}
    api-url: https://api.github.com
    rate-limit:
      requests-per-hour: 5000
      burst-size: 100
  
  security:
    jwt:
      secret: ${JWT_SECRET:default-jwt-secret-change-in-production}
      expiration: 86400000 # 24 hours
  
  scanning:
    interval-minutes: ${SCAN_INTERVAL:15}
    batch-size: 10
    max-file-size-kb: 1024
    excluded-extensions:
      - .jpg
      - .jpeg
      - .png
      - .gif
      - .pdf
      - .zip
      - .tar
      - .gz
    excluded-paths:
      - "**/test/**"
      - "**/tests/**"
      - "**/spec/**"
      - "**/docs/**"
      - "**/*.md"
      - "**/node_modules/**"
      - "**/vendor/**"
  
  notifications:
    email:
      enabled: ${EMAIL_NOTIFICATIONS_ENABLED:false}
      from: ${EMAIL_FROM:<EMAIL>}
      admin-emails: ${ADMIN_EMAILS:}

---
spring:
  config:
    activate:
      on-profile: docker
  
  datasource:
    url: **********************************************

---
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
  
  flyway:
    enabled: false
