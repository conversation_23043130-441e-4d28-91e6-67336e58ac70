package com.github.secrets.repository;

import com.github.secrets.entity.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@org.springframework.stereotype.Repository
public interface RepositoryRepository extends JpaRepository<Repository, Long> {
    
    Optional<Repository> findByFullName(String fullName);
    
    Optional<Repository> findByGithubId(Long githubId);
    
    List<Repository> findByOwner(String owner);
    
    List<Repository> findByIsActiveTrue();
    
    @Query("SELECT r FROM Repository r WHERE r.isActive = true AND " +
           "(r.lastScanAt IS NULL OR r.lastScanAt < :cutoffTime)")
    List<Repository> findRepositoriesNeedingScanning(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    @Query("SELECT r FROM Repository r WHERE r.isActive = true AND " +
           "r.scanFrequencyMinutes <= :maxFrequency")
    List<Repository> findActiveRepositoriesByMaxFrequency(@Param("maxFrequency") Integer maxFrequency);
    
    @Query("SELECT r FROM Repository r WHERE " +
           "(LOWER(r.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(r.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(r.owner) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "AND r.isActive = true")
    Page<Repository> searchActiveRepositories(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT COUNT(r) FROM Repository r WHERE r.isActive = true")
    long countActiveRepositories();
    
    @Query("SELECT COUNT(r) FROM Repository r WHERE r.isActive = true AND r.isPrivate = true")
    long countPrivateRepositories();
    
    @Query("SELECT COUNT(r) FROM Repository r WHERE r.isActive = true AND r.isPrivate = false")
    long countPublicRepositories();
    
    @Query("SELECT r FROM Repository r WHERE r.addedByUser.id = :userId AND r.isActive = true")
    List<Repository> findByAddedByUserId(@Param("userId") Long userId);
    
    @Query("SELECT r FROM Repository r WHERE r.isActive = true ORDER BY r.lastScanAt ASC NULLS FIRST")
    List<Repository> findActiveRepositoriesOrderByLastScanAsc();
    
    @Query("SELECT r FROM Repository r WHERE r.isActive = true AND r.lastScanAt IS NOT NULL " +
           "ORDER BY r.lastScanAt DESC")
    List<Repository> findRecentlyScannedRepositories(Pageable pageable);
}
