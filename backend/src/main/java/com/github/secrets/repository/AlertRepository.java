package com.github.secrets.repository;

import com.github.secrets.entity.Alert;
import com.github.secrets.enums.AlertStatus;
import com.github.secrets.enums.Severity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AlertRepository extends JpaRepository<Alert, Long> {
    
    List<Alert> findByRepositoryId(Long repositoryId);
    
    List<Alert> findByStatus(AlertStatus status);
    
    List<Alert> findBySeverity(Severity severity);
    
    Page<Alert> findByRepositoryIdAndStatus(Long repositoryId, AlertStatus status, Pageable pageable);
    
    @Query("SELECT a FROM Alert a WHERE a.status = :status ORDER BY a.createdAt DESC")
    Page<Alert> findByStatusOrderByCreatedAtDesc(@Param("status") AlertStatus status, Pageable pageable);
    
    @Query("SELECT a FROM Alert a WHERE a.severity = :severity AND a.status = :status " +
           "ORDER BY a.createdAt DESC")
    Page<Alert> findBySeverityAndStatusOrderByCreatedAtDesc(
            @Param("severity") Severity severity, 
            @Param("status") AlertStatus status, 
            Pageable pageable);
    
    @Query("SELECT a FROM Alert a WHERE a.createdAt >= :since ORDER BY a.createdAt DESC")
    List<Alert> findRecentAlerts(@Param("since") LocalDateTime since);
    
    @Query("SELECT COUNT(a) FROM Alert a WHERE a.status = :status")
    long countByStatus(@Param("status") AlertStatus status);
    
    @Query("SELECT COUNT(a) FROM Alert a WHERE a.severity = :severity AND a.status = :status")
    long countBySeverityAndStatus(@Param("severity") Severity severity, @Param("status") AlertStatus status);
    
    @Query("SELECT COUNT(a) FROM Alert a WHERE a.repository.id = :repositoryId AND a.status = :status")
    long countByRepositoryIdAndStatus(@Param("repositoryId") Long repositoryId, @Param("status") AlertStatus status);
    
    @Query("SELECT a FROM Alert a WHERE a.repository.id = :repositoryId AND " +
           "a.filePath = :filePath AND a.lineNumber = :lineNumber AND a.commitSha = :commitSha")
    List<Alert> findDuplicateAlerts(@Param("repositoryId") Long repositoryId,
                                   @Param("filePath") String filePath,
                                   @Param("lineNumber") Integer lineNumber,
                                   @Param("commitSha") String commitSha);
    
    @Query("SELECT a FROM Alert a WHERE " +
           "(LOWER(a.filePath) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(a.secretType) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(a.repository.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "ORDER BY a.createdAt DESC")
    Page<Alert> searchAlerts(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT a.secretType, COUNT(a) FROM Alert a WHERE a.status = :status " +
           "GROUP BY a.secretType ORDER BY COUNT(a) DESC")
    List<Object[]> getSecretTypeStatistics(@Param("status") AlertStatus status);
    
    @Query("SELECT a.severity, COUNT(a) FROM Alert a WHERE a.status = :status " +
           "GROUP BY a.severity ORDER BY a.severity DESC")
    List<Object[]> getSeverityStatistics(@Param("status") AlertStatus status);
    
    @Query("SELECT DATE(a.createdAt), COUNT(a) FROM Alert a WHERE a.createdAt >= :since " +
           "GROUP BY DATE(a.createdAt) ORDER BY DATE(a.createdAt)")
    List<Object[]> getAlertTrends(@Param("since") LocalDateTime since);
    
    @Query("SELECT a FROM Alert a WHERE a.repository.id IN :repositoryIds AND a.status = :status " +
           "ORDER BY a.createdAt DESC")
    Page<Alert> findByRepositoryIdsAndStatus(@Param("repositoryIds") List<Long> repositoryIds,
                                            @Param("status") AlertStatus status,
                                            Pageable pageable);
}
