package com.github.secrets.repository;

import com.github.secrets.entity.SecretPattern;
import com.github.secrets.enums.Severity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SecretPatternRepository extends JpaRepository<SecretPattern, Long> {
    
    List<SecretPattern> findByIsActiveTrue();
    
    List<SecretPattern> findBySecretType(String secretType);
    
    List<SecretPattern> findBySeverity(Severity severity);
    
    Optional<SecretPattern> findByName(String name);
    
    @Query("SELECT sp FROM SecretPattern sp WHERE sp.isActive = true AND sp.severity = :severity")
    List<SecretPattern> findActivePatternsBySeverity(@Param("severity") Severity severity);
    
    @Query("SELECT sp FROM SecretPattern sp WHERE sp.isActive = true AND sp.secretType = :secretType")
    List<SecretPattern> findActivePatternsBySecretType(@Param("secretType") String secretType);
    
    @Query("SELECT DISTINCT sp.secretType FROM SecretPattern sp WHERE sp.isActive = true ORDER BY sp.secretType")
    List<String> findDistinctActiveSecretTypes();
    
    @Query("SELECT COUNT(sp) FROM SecretPattern sp WHERE sp.isActive = true")
    long countActivePatterns();
    
    @Query("SELECT sp.secretType, COUNT(sp) FROM SecretPattern sp WHERE sp.isActive = true " +
           "GROUP BY sp.secretType ORDER BY COUNT(sp) DESC")
    List<Object[]> getPatternCountBySecretType();
    
    @Query("SELECT sp.severity, COUNT(sp) FROM SecretPattern sp WHERE sp.isActive = true " +
           "GROUP BY sp.severity ORDER BY sp.severity DESC")
    List<Object[]> getPatternCountBySeverity();
}
