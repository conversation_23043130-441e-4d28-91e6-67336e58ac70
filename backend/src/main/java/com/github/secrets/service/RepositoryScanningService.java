package com.github.secrets.service;

import com.github.secrets.dto.SecretMatch;
import com.github.secrets.dto.github.GitHubCommit;
import com.github.secrets.dto.github.GitHubContent;
import com.github.secrets.entity.*;
import com.github.secrets.enums.AlertStatus;
import com.github.secrets.enums.ScanJobStatus;
import com.github.secrets.exception.GitHubApiException;
import com.github.secrets.exception.RateLimitExceededException;
import com.github.secrets.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class RepositoryScanningService {
    
    private static final Logger logger = LoggerFactory.getLogger(RepositoryScanningService.class);
    
    private final RepositoryRepository repositoryRepository;
    private final ScanJobRepository scanJobRepository;
    private final AlertRepository alertRepository;
    private final SecretPatternRepository secretPatternRepository;
    private final GitHubApiService gitHubApiService;
    private final SecretDetectionService secretDetectionService;
    private final NotificationService notificationService;
    
    @Value("${app.scanning.batch-size:10}")
    private int batchSize;
    
    @Value("${app.scanning.max-file-size-kb:1024}")
    private int maxFileSizeKb;
    
    public RepositoryScanningService(RepositoryRepository repositoryRepository,
                                   ScanJobRepository scanJobRepository,
                                   AlertRepository alertRepository,
                                   SecretPatternRepository secretPatternRepository,
                                   GitHubApiService gitHubApiService,
                                   SecretDetectionService secretDetectionService,
                                   NotificationService notificationService) {
        this.repositoryRepository = repositoryRepository;
        this.scanJobRepository = scanJobRepository;
        this.alertRepository = alertRepository;
        this.secretPatternRepository = secretPatternRepository;
        this.gitHubApiService = gitHubApiService;
        this.secretDetectionService = secretDetectionService;
        this.notificationService = notificationService;
    }
    
    /**
     * Scan a specific repository
     */
    @Transactional
    public ScanJob scanRepository(Long repositoryId, String scanType) {
        Optional<Repository> repoOpt = repositoryRepository.findById(repositoryId);
        if (repoOpt.isEmpty()) {
            throw new IllegalArgumentException("Repository not found: " + repositoryId);
        }
        
        Repository repository = repoOpt.get();
        if (!repository.getIsActive()) {
            throw new IllegalArgumentException("Repository is not active: " + repository.getFullName());
        }
        
        // Create scan job
        ScanJob scanJob = new ScanJob(repository, scanType);
        scanJob = scanJobRepository.save(scanJob);
        
        try {
            executeScan(scanJob);
        } catch (Exception e) {
            logger.error("Error scanning repository {}: {}", repository.getFullName(), e.getMessage(), e);
            scanJob.markAsFailed(e.getMessage());
            scanJobRepository.save(scanJob);
        }
        
        return scanJob;
    }
    
    /**
     * Execute the actual scanning process
     */
    private void executeScan(ScanJob scanJob) {
        Repository repository = scanJob.getRepository();
        scanJob.markAsStarted();
        scanJobRepository.save(scanJob);
        
        logger.info("Starting scan for repository: {}", repository.getFullName());
        
        try {
            // Check rate limit
            if (!gitHubApiService.hasRateLimitRemaining()) {
                throw new RateLimitExceededException("GitHub API rate limit exceeded");
            }
            
            // Get recent commits to scan
            List<GitHubCommit> commits = getCommitsToScan(repository);
            
            if (commits.isEmpty()) {
                logger.info("No new commits to scan for repository: {}", repository.getFullName());
                scanJob.markAsCompleted();
                scanJobRepository.save(scanJob);
                return;
            }
            
            // Scan each commit
            int totalFilesScanned = 0;
            int totalSecretsFound = 0;
            
            for (GitHubCommit commit : commits) {
                ScanResult result = scanCommit(repository, commit, scanJob);
                totalFilesScanned += result.filesScanned;
                totalSecretsFound += result.secretsFound;
                
                // Update scan job progress
                scanJob.setFilesScanned(totalFilesScanned);
                scanJob.setSecretsFound(totalSecretsFound);
                scanJob.setCommitSha(commit.getSha());
                scanJobRepository.save(scanJob);
                
                // Check rate limit periodically
                if (!gitHubApiService.hasRateLimitRemaining()) {
                    logger.warn("Rate limit reached during scan of repository: {}", repository.getFullName());
                    break;
                }
            }
            
            // Update repository last scan info
            repository.setLastScanAt(LocalDateTime.now());
            if (!commits.isEmpty()) {
                repository.setLastCommitSha(commits.get(0).getSha());
            }
            repositoryRepository.save(repository);
            
            scanJob.markAsCompleted();
            scanJobRepository.save(scanJob);
            
            logger.info("Completed scan for repository: {} - Files: {}, Secrets: {}", 
                repository.getFullName(), totalFilesScanned, totalSecretsFound);
            
        } catch (RateLimitExceededException e) {
            logger.warn("Rate limit exceeded during scan: {}", e.getMessage());
            scanJob.markAsFailed("Rate limit exceeded: " + e.getMessage());
            scanJobRepository.save(scanJob);
        } catch (GitHubApiException e) {
            logger.error("GitHub API error during scan: {}", e.getMessage());
            scanJob.markAsFailed("GitHub API error: " + e.getMessage());
            scanJobRepository.save(scanJob);
        } catch (Exception e) {
            logger.error("Unexpected error during scan: {}", e.getMessage(), e);
            scanJob.markAsFailed("Unexpected error: " + e.getMessage());
            scanJobRepository.save(scanJob);
        }
    }
    
    /**
     * Get commits that need to be scanned
     */
    private List<GitHubCommit> getCommitsToScan(Repository repository) {
        String[] parts = repository.getFullName().split("/");
        String owner = parts[0];
        String repo = parts[1];
        
        // Get commits since last scan
        String since = null;
        if (repository.getLastScanAt() != null) {
            since = repository.getLastScanAt().toString();
        }
        
        return gitHubApiService.getCommits(owner, repo, since, 1, 10); // Limit to recent commits
    }
    
    /**
     * Scan a specific commit
     */
    private ScanResult scanCommit(Repository repository, GitHubCommit commit, ScanJob scanJob) {
        logger.debug("Scanning commit {} in repository {}", commit.getSha(), repository.getFullName());
        
        String[] parts = repository.getFullName().split("/");
        String owner = parts[0];
        String repo = parts[1];
        
        int filesScanned = 0;
        int secretsFound = 0;
        
        try {
            // Get repository contents at this commit
            List<GitHubContent> contents = getRepositoryContents(owner, repo, "", commit.getSha());
            
            for (GitHubContent content : contents) {
                if (content.isFile() && shouldScanFile(content)) {
                    ScanResult fileResult = scanFile(repository, content, commit.getSha(), scanJob);
                    filesScanned += fileResult.filesScanned;
                    secretsFound += fileResult.secretsFound;
                }
            }
            
        } catch (Exception e) {
            logger.warn("Error scanning commit {} in repository {}: {}", 
                commit.getSha(), repository.getFullName(), e.getMessage());
        }
        
        return new ScanResult(filesScanned, secretsFound);
    }
    
    /**
     * Get repository contents recursively
     */
    private List<GitHubContent> getRepositoryContents(String owner, String repo, String path, String ref) {
        List<GitHubContent> allContents = new ArrayList<>();
        
        try {
            List<GitHubContent> contents = gitHubApiService.getDirectoryContents(owner, repo, path, ref);
            
            for (GitHubContent content : contents) {
                if (content.isFile()) {
                    allContents.add(content);
                } else if (content.isDirectory() && shouldScanDirectory(content.getPath())) {
                    // Recursively get directory contents (with depth limit)
                    if (path.split("/").length < 5) { // Limit recursion depth
                        allContents.addAll(getRepositoryContents(owner, repo, content.getPath(), ref));
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("Error getting contents for path {} in {}/{}: {}", path, owner, repo, e.getMessage());
        }
        
        return allContents;
    }
    
    /**
     * Scan a specific file
     */
    private ScanResult scanFile(Repository repository, GitHubContent fileContent, String commitSha, ScanJob scanJob) {
        if (fileContent.getSize() > maxFileSizeKb * 1024) {
            logger.debug("Skipping large file: {} ({} bytes)", fileContent.getPath(), fileContent.getSize());
            return new ScanResult(1, 0); // Count as scanned but no secrets
        }
        
        try {
            String[] parts = repository.getFullName().split("/");
            String owner = parts[0];
            String repo = parts[1];
            
            // Get file content
            GitHubContent fullContent = gitHubApiService.getFileContent(owner, repo, fileContent.getPath(), commitSha);
            String content = gitHubApiService.decodeContent(fullContent.getContent());
            
            // Scan for secrets
            List<SecretMatch> matches = secretDetectionService.scanFileContent(fileContent.getPath(), content);
            
            // Create alerts for matches
            for (SecretMatch match : matches) {
                createAlert(repository, match, commitSha, scanJob);
            }
            
            return new ScanResult(1, matches.size());
            
        } catch (Exception e) {
            logger.warn("Error scanning file {} in repository {}: {}", 
                fileContent.getPath(), repository.getFullName(), e.getMessage());
            return new ScanResult(1, 0);
        }
    }
    
    /**
     * Create an alert from a secret match
     */
    @Transactional
    private void createAlert(Repository repository, SecretMatch match, String commitSha, ScanJob scanJob) {
        // Check for duplicate alerts
        List<Alert> existingAlerts = alertRepository.findDuplicateAlerts(
            repository.getId(), match.getFilePath(), match.getLineNumber(), commitSha
        );
        
        if (!existingAlerts.isEmpty()) {
            logger.debug("Duplicate alert found for {}:{} in repository {}", 
                match.getFilePath(), match.getLineNumber(), repository.getFullName());
            return;
        }
        
        Alert alert = new Alert(
            repository,
            match.getFilePath(),
            match.getLineNumber(),
            commitSha,
            match.getSecretType(),
            match.getSeverity(),
            match.getConfidenceScore()
        );
        
        alert.setScanJob(scanJob);
        alert.setSecretPattern(match.getPattern());
        alert.setPartialSecret(match.getPartialSecret());
        alert.setContextBefore(match.getContextBefore());
        alert.setContextAfter(match.getContextAfter());
        alert.setStatus(AlertStatus.OPEN);
        
        alert = alertRepository.save(alert);
        
        logger.info("Created alert for {} secret in {}:{} (confidence: {})", 
            match.getSecretType(), match.getFilePath(), match.getLineNumber(), match.getConfidenceScore());
        
        // Send notification for high-severity alerts
        if (match.getSeverity().name().equals("HIGH")) {
            notificationService.sendAlertNotification(alert);
        }
    }
    
    /**
     * Check if file should be scanned
     */
    private boolean shouldScanFile(GitHubContent content) {
        String fileName = content.getName().toLowerCase();
        
        // Skip binary files
        String[] binaryExtensions = {".jpg", ".jpeg", ".png", ".gif", ".pdf", ".zip", ".tar", ".gz", ".exe", ".dll"};
        for (String ext : binaryExtensions) {
            if (fileName.endsWith(ext)) {
                return false;
            }
        }
        
        // Skip large files
        if (content.getSize() > maxFileSizeKb * 1024) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if directory should be scanned
     */
    private boolean shouldScanDirectory(String path) {
        String lowerPath = path.toLowerCase();
        
        // Skip common directories that don't contain source code
        String[] skipDirs = {"node_modules", ".git", "vendor", "target", "build", "dist", ".idea", ".vscode"};
        for (String skipDir : skipDirs) {
            if (lowerPath.contains(skipDir)) {
                return false;
            }
        }
        
        return true;
    }
    
    private static class ScanResult {
        final int filesScanned;
        final int secretsFound;
        
        ScanResult(int filesScanned, int secretsFound) {
            this.filesScanned = filesScanned;
            this.secretsFound = secretsFound;
        }
    }
}
