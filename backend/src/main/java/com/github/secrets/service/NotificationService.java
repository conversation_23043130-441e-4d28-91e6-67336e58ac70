package com.github.secrets.service;

import com.github.secrets.entity.Alert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
public class NotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);
    
    private final JavaMailSender mailSender;
    
    @Value("${app.notifications.email.enabled:false}")
    private boolean emailNotificationsEnabled;
    
    @Value("${app.notifications.email.from:<EMAIL>}")
    private String fromEmail;
    
    @Value("${app.notifications.email.admin-emails:}")
    private String[] adminEmails;
    
    public NotificationService(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }
    
    /**
     * Send notification for a new alert
     */
    public void sendAlertNotification(Alert alert) {
        if (!emailNotificationsEnabled || adminEmails.length == 0) {
            logger.debug("Email notifications disabled or no admin emails configured");
            return;
        }
        
        try {
            String subject = String.format("🚨 Secret Detected: %s in %s", 
                alert.getSecretType(), alert.getRepository().getFullName());
            
            String body = buildAlertEmailBody(alert);
            
            for (String adminEmail : adminEmails) {
                sendEmail(adminEmail, subject, body);
            }
            
            logger.info("Sent alert notification for {} in {}", 
                alert.getSecretType(), alert.getRepository().getFullName());
            
        } catch (Exception e) {
            logger.error("Failed to send alert notification: {}", e.getMessage(), e);
        }
    }
    
    private String buildAlertEmailBody(Alert alert) {
        StringBuilder body = new StringBuilder();
        
        body.append("A potential secret has been detected in your GitHub repository.\n\n");
        
        body.append("Details:\n");
        body.append("Repository: ").append(alert.getRepository().getFullName()).append("\n");
        body.append("File: ").append(alert.getFilePath()).append("\n");
        body.append("Line: ").append(alert.getLineNumber()).append("\n");
        body.append("Secret Type: ").append(alert.getSecretType()).append("\n");
        body.append("Severity: ").append(alert.getSeverity()).append("\n");
        body.append("Confidence: ").append(alert.getConfidenceScore()).append("\n");
        body.append("Commit: ").append(alert.getCommitSha()).append("\n");
        body.append("Detected: ").append(alert.getCreatedAt()).append("\n\n");
        
        if (alert.getPartialSecret() != null) {
            body.append("Partial Secret: ").append(alert.getPartialSecret()).append("\n\n");
        }
        
        body.append("Recommended Actions:\n");
        body.append("1. Immediately revoke and regenerate the exposed secret\n");
        body.append("2. Remove the secret from the repository history\n");
        body.append("3. Review access logs for potential unauthorized usage\n");
        body.append("4. Update your application to use the new secret\n\n");
        
        body.append("View this alert in the GitHub Secrets Scanner dashboard.\n");
        
        return body.toString();
    }
    
    private void sendEmail(String to, String subject, String body) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(body);
            
            mailSender.send(message);
            
            logger.debug("Email sent to: {}", to);
            
        } catch (Exception e) {
            logger.error("Failed to send email to {}: {}", to, e.getMessage());
        }
    }
}
