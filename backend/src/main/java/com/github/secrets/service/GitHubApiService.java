package com.github.secrets.service;

import com.github.secrets.dto.github.GitHubCommit;
import com.github.secrets.dto.github.GitHubContent;
import com.github.secrets.dto.github.GitHubRepository;
import com.github.secrets.exception.GitHubApiException;
import com.github.secrets.exception.RateLimitExceededException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class GitHubApiService {
    
    private static final Logger logger = LoggerFactory.getLogger(GitHubApiService.class);
    
    private final WebClient webClient;
    private final AtomicInteger rateLimitRemaining = new AtomicInteger(5000);
    private final AtomicLong rateLimitResetTime = new AtomicLong(0);
    
    @Value("${app.github.api-url:https://api.github.com}")
    private String githubApiUrl;
    
    @Value("${app.github.token}")
    private String githubToken;
    
    @Value("${app.github.rate-limit.requests-per-hour:5000}")
    private int maxRequestsPerHour;
    
    public GitHubApiService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder
                .baseUrl(githubApiUrl)
                .defaultHeader(HttpHeaders.ACCEPT, "application/vnd.github.v3+json")
                .defaultHeader(HttpHeaders.USER_AGENT, "GitHub-Secrets-Scanner/1.0")
                .build();
    }
    
    /**
     * Get repository information by owner and name
     */
    public GitHubRepository getRepository(String owner, String repo) {
        return executeWithRateLimit(() -> 
            webClient.get()
                .uri("/repos/{owner}/{repo}", owner, repo)
                .header(HttpHeaders.AUTHORIZATION, "token " + githubToken)
                .retrieve()
                .bodyToMono(GitHubRepository.class)
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                    .filter(this::isRetryableException))
                .block()
        );
    }
    
    /**
     * Get repositories for a user or organization
     */
    public List<GitHubRepository> getUserRepositories(String username, int page, int perPage) {
        return executeWithRateLimit(() -> 
            webClient.get()
                .uri(uriBuilder -> uriBuilder
                    .path("/users/{username}/repos")
                    .queryParam("page", page)
                    .queryParam("per_page", perPage)
                    .queryParam("sort", "updated")
                    .queryParam("direction", "desc")
                    .build(username))
                .header(HttpHeaders.AUTHORIZATION, "token " + githubToken)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<List<GitHubRepository>>() {})
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                    .filter(this::isRetryableException))
                .block()
        );
    }
    
    /**
     * Get organization repositories
     */
    public List<GitHubRepository> getOrganizationRepositories(String org, int page, int perPage) {
        return executeWithRateLimit(() -> 
            webClient.get()
                .uri(uriBuilder -> uriBuilder
                    .path("/orgs/{org}/repos")
                    .queryParam("page", page)
                    .queryParam("per_page", perPage)
                    .queryParam("sort", "updated")
                    .queryParam("direction", "desc")
                    .build(org))
                .header(HttpHeaders.AUTHORIZATION, "token " + githubToken)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<List<GitHubRepository>>() {})
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                    .filter(this::isRetryableException))
                .block()
        );
    }
    
    /**
     * Get recent commits for a repository
     */
    public List<GitHubCommit> getCommits(String owner, String repo, String since, int page, int perPage) {
        return executeWithRateLimit(() -> 
            webClient.get()
                .uri(uriBuilder -> {
                    var builder = uriBuilder
                        .path("/repos/{owner}/{repo}/commits")
                        .queryParam("page", page)
                        .queryParam("per_page", perPage);
                    if (since != null) {
                        builder.queryParam("since", since);
                    }
                    return builder.build(owner, repo);
                })
                .header(HttpHeaders.AUTHORIZATION, "token " + githubToken)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<List<GitHubCommit>>() {})
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                    .filter(this::isRetryableException))
                .block()
        );
    }
    
    /**
     * Get file content from repository
     */
    public GitHubContent getFileContent(String owner, String repo, String path, String ref) {
        return executeWithRateLimit(() -> 
            webClient.get()
                .uri(uriBuilder -> {
                    var builder = uriBuilder.path("/repos/{owner}/{repo}/contents/{path}");
                    if (ref != null) {
                        builder.queryParam("ref", ref);
                    }
                    return builder.build(owner, repo, path);
                })
                .header(HttpHeaders.AUTHORIZATION, "token " + githubToken)
                .retrieve()
                .bodyToMono(GitHubContent.class)
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                    .filter(this::isRetryableException))
                .block()
        );
    }
    
    /**
     * Get directory contents from repository
     */
    public List<GitHubContent> getDirectoryContents(String owner, String repo, String path, String ref) {
        return executeWithRateLimit(() -> 
            webClient.get()
                .uri(uriBuilder -> {
                    var builder = uriBuilder.path("/repos/{owner}/{repo}/contents/{path}");
                    if (ref != null) {
                        builder.queryParam("ref", ref);
                    }
                    return builder.build(owner, repo, path);
                })
                .header(HttpHeaders.AUTHORIZATION, "token " + githubToken)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<List<GitHubContent>>() {})
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                    .filter(this::isRetryableException))
                .block()
        );
    }
    
    /**
     * Decode base64 content from GitHub API
     */
    public String decodeContent(String base64Content) {
        if (base64Content == null || base64Content.trim().isEmpty()) {
            return "";
        }
        try {
            // Remove whitespace and newlines
            String cleanContent = base64Content.replaceAll("\\s", "");
            byte[] decodedBytes = Base64.getDecoder().decode(cleanContent);
            return new String(decodedBytes);
        } catch (Exception e) {
            logger.warn("Failed to decode base64 content: {}", e.getMessage());
            return "";
        }
    }
    
    /**
     * Check if we have sufficient rate limit remaining
     */
    public boolean hasRateLimitRemaining() {
        return rateLimitRemaining.get() > 10; // Keep some buffer
    }
    
    /**
     * Get current rate limit status
     */
    public RateLimitStatus getRateLimitStatus() {
        return new RateLimitStatus(
            rateLimitRemaining.get(),
            LocalDateTime.ofEpochSecond(rateLimitResetTime.get(), 0, java.time.ZoneOffset.UTC)
        );
    }
    
    private <T> T executeWithRateLimit(java.util.function.Supplier<T> operation) {
        // Check rate limit before making request
        if (!hasRateLimitRemaining()) {
            long resetTime = rateLimitResetTime.get();
            long currentTime = System.currentTimeMillis() / 1000;
            if (currentTime < resetTime) {
                throw new RateLimitExceededException(
                    "GitHub API rate limit exceeded. Reset at: " + 
                    LocalDateTime.ofEpochSecond(resetTime, 0, java.time.ZoneOffset.UTC)
                );
            }
        }
        
        try {
            T result = operation.get();
            // Update rate limit info from response headers if available
            // This would be done in a WebClient filter in a real implementation
            return result;
        } catch (WebClientResponseException e) {
            updateRateLimitFromHeaders(e.getHeaders());
            
            if (e.getStatusCode() == HttpStatus.FORBIDDEN && 
                e.getResponseBodyAsString().contains("rate limit")) {
                throw new RateLimitExceededException("GitHub API rate limit exceeded");
            }
            
            throw new GitHubApiException("GitHub API error: " + e.getMessage(), e);
        }
    }
    
    private void updateRateLimitFromHeaders(HttpHeaders headers) {
        try {
            String remaining = headers.getFirst("X-RateLimit-Remaining");
            String reset = headers.getFirst("X-RateLimit-Reset");
            
            if (remaining != null) {
                rateLimitRemaining.set(Integer.parseInt(remaining));
            }
            if (reset != null) {
                rateLimitResetTime.set(Long.parseLong(reset));
            }
        } catch (NumberFormatException e) {
            logger.warn("Failed to parse rate limit headers: {}", e.getMessage());
        }
    }
    
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            HttpStatus status = ex.getStatusCode();
            return status.is5xxServerError() || 
                   status == HttpStatus.TOO_MANY_REQUESTS ||
                   status == HttpStatus.REQUEST_TIMEOUT;
        }
        return false;
    }
    
    public static class RateLimitStatus {
        private final int remaining;
        private final LocalDateTime resetTime;
        
        public RateLimitStatus(int remaining, LocalDateTime resetTime) {
            this.remaining = remaining;
            this.resetTime = resetTime;
        }
        
        public int getRemaining() {
            return remaining;
        }
        
        public LocalDateTime getResetTime() {
            return resetTime;
        }
        
        public boolean isExceeded() {
            return remaining <= 0;
        }
    }
}
