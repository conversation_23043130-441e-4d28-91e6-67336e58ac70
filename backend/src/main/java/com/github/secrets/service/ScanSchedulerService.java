package com.github.secrets.service;

import com.github.secrets.entity.Repository;
import com.github.secrets.entity.ScanJob;
import com.github.secrets.enums.ScanJobStatus;
import com.github.secrets.repository.RepositoryRepository;
import com.github.secrets.repository.ScanJobRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;

@Service
public class ScanSchedulerService {
    
    private static final Logger logger = LoggerFactory.getLogger(ScanSchedulerService.class);
    
    private final RepositoryRepository repositoryRepository;
    private final ScanJobRepository scanJobRepository;
    private final RepositoryScanningService repositoryScanningService;
    private final GitHubApiService gitHubApiService;
    
    @Value("${app.scanning.interval-minutes:15}")
    private int scanIntervalMinutes;
    
    @Value("${app.scanning.batch-size:10}")
    private int batchSize;
    
    @Value("${app.scanning.max-concurrent-scans:3}")
    private int maxConcurrentScans;
    
    // Semaphore to limit concurrent scans
    private final Semaphore scanSemaphore;
    
    public ScanSchedulerService(RepositoryRepository repositoryRepository,
                               ScanJobRepository scanJobRepository,
                               RepositoryScanningService repositoryScanningService,
                               GitHubApiService gitHubApiService,
                               @Value("${app.scanning.max-concurrent-scans:3}") int maxConcurrentScans) {
        this.repositoryRepository = repositoryRepository;
        this.scanJobRepository = scanJobRepository;
        this.repositoryScanningService = repositoryScanningService;
        this.gitHubApiService = gitHubApiService;
        this.scanSemaphore = new Semaphore(maxConcurrentScans);
    }
    
    /**
     * Scheduled task to scan repositories
     * Runs every 5 minutes to check for repositories that need scanning
     */
    @Scheduled(fixedRateString = "${app.scanning.check-interval-ms:300000}") // 5 minutes
    public void scheduleRepositoryScans() {
        logger.debug("Checking for repositories that need scanning...");
        
        try {
            // Check rate limit before scheduling scans
            if (!gitHubApiService.hasRateLimitRemaining()) {
                logger.warn("GitHub API rate limit exceeded, skipping scheduled scans");
                return;
            }
            
            // Find repositories that need scanning
            LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(scanIntervalMinutes);
            List<Repository> repositoriesToScan = repositoryRepository.findRepositoriesNeedingScanning(cutoffTime);
            
            if (repositoriesToScan.isEmpty()) {
                logger.debug("No repositories need scanning at this time");
                return;
            }
            
            logger.info("Found {} repositories that need scanning", repositoriesToScan.size());
            
            // Process repositories in batches
            for (int i = 0; i < repositoriesToScan.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, repositoriesToScan.size());
                List<Repository> batch = repositoriesToScan.subList(i, endIndex);
                
                processBatch(batch);
                
                // Check rate limit between batches
                if (!gitHubApiService.hasRateLimitRemaining()) {
                    logger.warn("Rate limit reached, stopping scheduled scans");
                    break;
                }
            }
            
        } catch (Exception e) {
            logger.error("Error in scheduled repository scanning: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Process a batch of repositories for scanning
     */
    private void processBatch(List<Repository> repositories) {
        for (Repository repository : repositories) {
            try {
                // Check if repository already has a pending or running scan
                if (hasActiveScan(repository)) {
                    logger.debug("Repository {} already has an active scan, skipping", repository.getFullName());
                    continue;
                }
                
                // Schedule async scan
                scheduleAsyncScan(repository);
                
            } catch (Exception e) {
                logger.error("Error processing repository {} for scanning: {}", 
                    repository.getFullName(), e.getMessage(), e);
            }
        }
    }
    
    /**
     * Check if repository has an active scan job
     */
    private boolean hasActiveScan(Repository repository) {
        List<ScanJob> activeJobs = scanJobRepository.findByRepositoryIdAndStatusOrderByCreatedAtDesc(
            repository.getId(), ScanJobStatus.RUNNING
        );
        
        if (!activeJobs.isEmpty()) {
            return true;
        }
        
        // Also check for recent pending jobs
        List<ScanJob> pendingJobs = scanJobRepository.findByRepositoryIdAndStatusOrderByCreatedAtDesc(
            repository.getId(), ScanJobStatus.PENDING
        );
        
        return !pendingJobs.isEmpty();
    }
    
    /**
     * Schedule an asynchronous scan for a repository
     */
    @Async
    public CompletableFuture<Void> scheduleAsyncScan(Repository repository) {
        return CompletableFuture.runAsync(() -> {
            try {
                // Acquire semaphore to limit concurrent scans
                scanSemaphore.acquire();
                
                logger.info("Starting scheduled scan for repository: {}", repository.getFullName());
                
                // Perform the scan
                ScanJob scanJob = repositoryScanningService.scanRepository(repository.getId(), "SCHEDULED");
                
                logger.info("Completed scheduled scan for repository: {} (Status: {})", 
                    repository.getFullName(), scanJob.getStatus());
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("Scan interrupted for repository: {}", repository.getFullName());
            } catch (Exception e) {
                logger.error("Error in async scan for repository {}: {}", 
                    repository.getFullName(), e.getMessage(), e);
            } finally {
                // Release semaphore
                scanSemaphore.release();
            }
        });
    }
    
    /**
     * Clean up stuck scan jobs
     * Runs every hour to find and clean up jobs that have been running too long
     */
    @Scheduled(fixedRateString = "${app.scanning.cleanup-interval-ms:3600000}") // 1 hour
    public void cleanupStuckJobs() {
        logger.debug("Checking for stuck scan jobs...");
        
        try {
            // Find jobs that have been running for more than 2 hours
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(2);
            List<ScanJob> stuckJobs = scanJobRepository.findStuckJobs(cutoffTime);
            
            if (stuckJobs.isEmpty()) {
                logger.debug("No stuck scan jobs found");
                return;
            }
            
            logger.warn("Found {} stuck scan jobs, marking as failed", stuckJobs.size());
            
            for (ScanJob job : stuckJobs) {
                job.markAsFailed("Job timed out - exceeded maximum execution time");
                scanJobRepository.save(job);
                
                logger.warn("Marked stuck scan job {} as failed (Repository: {}, Started: {})", 
                    job.getId(), job.getRepository().getFullName(), job.getStartedAt());
            }
            
        } catch (Exception e) {
            logger.error("Error cleaning up stuck jobs: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Generate scan statistics
     * Runs daily to log scanning statistics
     */
    @Scheduled(cron = "0 0 6 * * *") // Daily at 6 AM
    public void generateScanStatistics() {
        logger.info("Generating daily scan statistics...");
        
        try {
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            
            long totalJobs = scanJobRepository.countByStatus(ScanJobStatus.COMPLETED);
            long completedJobs = scanJobRepository.countByStatus(ScanJobStatus.COMPLETED);
            long failedJobs = scanJobRepository.countByStatus(ScanJobStatus.FAILED);
            long pendingJobs = scanJobRepository.countByStatus(ScanJobStatus.PENDING);
            long runningJobs = scanJobRepository.countByStatus(ScanJobStatus.RUNNING);
            
            List<ScanJob> recentJobs = scanJobRepository.findRecentScanJobs(yesterday);
            
            Double avgDuration = scanJobRepository.getAverageScanDurationInSeconds();
            
            logger.info("Scan Statistics - Total: {}, Completed: {}, Failed: {}, Pending: {}, Running: {}", 
                totalJobs, completedJobs, failedJobs, pendingJobs, runningJobs);
            
            logger.info("Recent jobs (last 24h): {}, Average duration: {} seconds", 
                recentJobs.size(), avgDuration != null ? String.format("%.2f", avgDuration) : "N/A");
            
            // Log rate limit status
            var rateLimitStatus = gitHubApiService.getRateLimitStatus();
            logger.info("GitHub API Rate Limit - Remaining: {}, Reset: {}", 
                rateLimitStatus.getRemaining(), rateLimitStatus.getResetTime());
            
        } catch (Exception e) {
            logger.error("Error generating scan statistics: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Manually trigger scan for a specific repository
     */
    public CompletableFuture<ScanJob> triggerManualScan(Long repositoryId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                scanSemaphore.acquire();
                
                logger.info("Starting manual scan for repository ID: {}", repositoryId);
                
                ScanJob scanJob = repositoryScanningService.scanRepository(repositoryId, "MANUAL");
                
                logger.info("Completed manual scan for repository ID: {} (Status: {})", 
                    repositoryId, scanJob.getStatus());
                
                return scanJob;
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Manual scan interrupted", e);
            } catch (Exception e) {
                logger.error("Error in manual scan for repository ID {}: {}", repositoryId, e.getMessage(), e);
                throw new RuntimeException("Manual scan failed", e);
            } finally {
                scanSemaphore.release();
            }
        });
    }
    
    /**
     * Get current scanning status
     */
    public ScanningStatus getScanningStatus() {
        int availableSlots = scanSemaphore.availablePermits();
        int activeScans = maxConcurrentScans - availableSlots;
        
        long pendingJobs = scanJobRepository.countByStatus(ScanJobStatus.PENDING);
        long runningJobs = scanJobRepository.countByStatus(ScanJobStatus.RUNNING);
        
        var rateLimitStatus = gitHubApiService.getRateLimitStatus();
        
        return new ScanningStatus(
            activeScans,
            maxConcurrentScans,
            pendingJobs,
            runningJobs,
            rateLimitStatus.getRemaining(),
            rateLimitStatus.getResetTime()
        );
    }
    
    public static class ScanningStatus {
        private final int activeScans;
        private final int maxConcurrentScans;
        private final long pendingJobs;
        private final long runningJobs;
        private final int rateLimitRemaining;
        private final LocalDateTime rateLimitReset;
        
        public ScanningStatus(int activeScans, int maxConcurrentScans, long pendingJobs, 
                            long runningJobs, int rateLimitRemaining, LocalDateTime rateLimitReset) {
            this.activeScans = activeScans;
            this.maxConcurrentScans = maxConcurrentScans;
            this.pendingJobs = pendingJobs;
            this.runningJobs = runningJobs;
            this.rateLimitRemaining = rateLimitRemaining;
            this.rateLimitReset = rateLimitReset;
        }
        
        // Getters
        public int getActiveScans() { return activeScans; }
        public int getMaxConcurrentScans() { return maxConcurrentScans; }
        public long getPendingJobs() { return pendingJobs; }
        public long getRunningJobs() { return runningJobs; }
        public int getRateLimitRemaining() { return rateLimitRemaining; }
        public LocalDateTime getRateLimitReset() { return rateLimitReset; }
    }
}
