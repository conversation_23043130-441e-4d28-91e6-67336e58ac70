package com.github.secrets.controller;

import com.github.secrets.dto.RepositoryDto;
import com.github.secrets.dto.github.GitHubRepository;
import com.github.secrets.entity.Repository;
import com.github.secrets.entity.ScanJob;
import com.github.secrets.entity.User;
import com.github.secrets.enums.AlertStatus;
import com.github.secrets.enums.Severity;
import com.github.secrets.repository.AlertRepository;
import com.github.secrets.repository.RepositoryRepository;
import com.github.secrets.repository.UserRepository;
import com.github.secrets.service.GitHubApiService;
import com.github.secrets.service.ScanSchedulerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/repositories")
@Tag(name = "Repository Management", description = "APIs for managing monitored repositories")
public class RepositoryController {
    
    private final RepositoryRepository repositoryRepository;
    private final UserRepository userRepository;
    private final AlertRepository alertRepository;
    private final GitHubApiService gitHubApiService;
    private final ScanSchedulerService scanSchedulerService;
    
    public RepositoryController(RepositoryRepository repositoryRepository,
                               UserRepository userRepository,
                               AlertRepository alertRepository,
                               GitHubApiService gitHubApiService,
                               ScanSchedulerService scanSchedulerService) {
        this.repositoryRepository = repositoryRepository;
        this.userRepository = userRepository;
        this.alertRepository = alertRepository;
        this.gitHubApiService = gitHubApiService;
        this.scanSchedulerService = scanSchedulerService;
    }
    
    @GetMapping
    @Operation(summary = "Get all repositories", description = "Retrieve all monitored repositories with pagination")
    public ResponseEntity<Page<RepositoryDto>> getAllRepositories(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Repository> repositories;
        if (search != null && !search.trim().isEmpty()) {
            repositories = repositoryRepository.searchActiveRepositories(search.trim(), pageable);
        } else {
            repositories = repositoryRepository.findAll(pageable);
        }
        
        Page<RepositoryDto> repositoryDtos = repositories.map(repo -> {
            RepositoryDto dto = new RepositoryDto(repo);
            // Add statistics
            dto.setTotalAlerts(alertRepository.countByRepositoryIdAndStatus(repo.getId(), AlertStatus.OPEN));
            dto.setOpenAlerts(alertRepository.countByRepositoryIdAndStatus(repo.getId(), AlertStatus.OPEN));
            dto.setHighSeverityAlerts(alertRepository.countBySeverityAndStatus(Severity.HIGH, AlertStatus.OPEN));
            return dto;
        });
        
        return ResponseEntity.ok(repositoryDtos);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get repository by ID", description = "Retrieve a specific repository by its ID")
    public ResponseEntity<RepositoryDto> getRepository(@PathVariable Long id) {
        Optional<Repository> repository = repositoryRepository.findById(id);
        
        if (repository.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        RepositoryDto dto = new RepositoryDto(repository.get());
        // Add statistics
        dto.setTotalAlerts(alertRepository.countByRepositoryIdAndStatus(id, AlertStatus.OPEN));
        dto.setOpenAlerts(alertRepository.countByRepositoryIdAndStatus(id, AlertStatus.OPEN));
        dto.setHighSeverityAlerts(alertRepository.countBySeverityAndStatus(Severity.HIGH, AlertStatus.OPEN));
        
        return ResponseEntity.ok(dto);
    }
    
    @PostMapping
    @Operation(summary = "Add repository", description = "Add a new repository for monitoring")
    public ResponseEntity<RepositoryDto> addRepository(@Valid @RequestBody RepositoryDto repositoryDto,
                                                      Authentication authentication) {
        // Check if repository already exists
        Optional<Repository> existing = repositoryRepository.findByFullName(repositoryDto.getFullName());
        if (existing.isPresent()) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
        
        // Get current user
        Optional<User> user = userRepository.findByUsername(authentication.getName());
        if (user.isEmpty()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        
        try {
            // Validate repository exists on GitHub
            String[] parts = repositoryDto.getFullName().split("/");
            if (parts.length != 2) {
                return ResponseEntity.badRequest().build();
            }
            
            GitHubRepository githubRepo = gitHubApiService.getRepository(parts[0], parts[1]);
            
            // Create repository entity
            Repository repository = new Repository(
                githubRepo.getName(),
                githubRepo.getFullName(),
                githubRepo.getOwner().getLogin(),
                githubRepo.getId(),
                githubRepo.getCloneUrl()
            );
            
            repository.setDefaultBranch(githubRepo.getDefaultBranch());
            repository.setIsPrivate(githubRepo.getIsPrivate());
            repository.setAddedByUser(user.get());
            repository.setScanFrequencyMinutes(repositoryDto.getScanFrequencyMinutes() != null ? 
                repositoryDto.getScanFrequencyMinutes() : 15);
            
            repository = repositoryRepository.save(repository);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(new RepositoryDto(repository));
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "Update repository", description = "Update repository settings")
    public ResponseEntity<RepositoryDto> updateRepository(@PathVariable Long id,
                                                         @Valid @RequestBody RepositoryDto repositoryDto) {
        Optional<Repository> repositoryOpt = repositoryRepository.findById(id);
        
        if (repositoryOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        Repository repository = repositoryOpt.get();
        
        // Update allowed fields
        if (repositoryDto.getScanFrequencyMinutes() != null) {
            repository.setScanFrequencyMinutes(repositoryDto.getScanFrequencyMinutes());
        }
        if (repositoryDto.getIsActive() != null) {
            repository.setIsActive(repositoryDto.getIsActive());
        }
        
        repository = repositoryRepository.save(repository);
        
        return ResponseEntity.ok(new RepositoryDto(repository));
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete repository", description = "Remove repository from monitoring")
    public ResponseEntity<Void> deleteRepository(@PathVariable Long id) {
        Optional<Repository> repository = repositoryRepository.findById(id);
        
        if (repository.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        // Soft delete by setting inactive
        Repository repo = repository.get();
        repo.setIsActive(false);
        repositoryRepository.save(repo);
        
        return ResponseEntity.noContent().build();
    }
    
    @PostMapping("/{id}/scan")
    @Operation(summary = "Trigger manual scan", description = "Manually trigger a scan for the repository")
    public ResponseEntity<String> triggerScan(@PathVariable Long id) {
        Optional<Repository> repository = repositoryRepository.findById(id);
        
        if (repository.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        if (!repository.get().getIsActive()) {
            return ResponseEntity.badRequest().body("Repository is not active");
        }
        
        try {
            CompletableFuture<ScanJob> scanJobFuture = scanSchedulerService.triggerManualScan(id);
            return ResponseEntity.accepted().body("Scan triggered successfully");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Failed to trigger scan: " + e.getMessage());
        }
    }
    
    @GetMapping("/search/github")
    @Operation(summary = "Search GitHub repositories", description = "Search for repositories on GitHub by username or organization")
    public ResponseEntity<List<RepositoryDto>> searchGitHubRepositories(
            @RequestParam String query,
            @RequestParam(defaultValue = "user") String type, // user or org
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int perPage) {
        
        try {
            List<GitHubRepository> githubRepos;
            
            if ("org".equalsIgnoreCase(type)) {
                githubRepos = gitHubApiService.getOrganizationRepositories(query, page, perPage);
            } else {
                githubRepos = gitHubApiService.getUserRepositories(query, page, perPage);
            }
            
            List<RepositoryDto> repositoryDtos = githubRepos.stream()
                .map(githubRepo -> {
                    RepositoryDto dto = new RepositoryDto();
                    dto.setName(githubRepo.getName());
                    dto.setFullName(githubRepo.getFullName());
                    dto.setOwner(githubRepo.getOwner().getLogin());
                    dto.setGithubId(githubRepo.getId());
                    dto.setCloneUrl(githubRepo.getCloneUrl());
                    dto.setDefaultBranch(githubRepo.getDefaultBranch());
                    dto.setIsPrivate(githubRepo.getIsPrivate());
                    return dto;
                })
                .collect(Collectors.toList());
            
            return ResponseEntity.ok(repositoryDtos);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/stats")
    @Operation(summary = "Get repository statistics", description = "Get overall repository statistics")
    public ResponseEntity<RepositoryStats> getRepositoryStats() {
        long totalRepositories = repositoryRepository.countActiveRepositories();
        long privateRepositories = repositoryRepository.countPrivateRepositories();
        long publicRepositories = repositoryRepository.countPublicRepositories();
        
        long totalAlerts = alertRepository.countByStatus(AlertStatus.OPEN);
        long highSeverityAlerts = alertRepository.countBySeverityAndStatus(Severity.HIGH, AlertStatus.OPEN);
        
        RepositoryStats stats = new RepositoryStats(
            totalRepositories,
            privateRepositories,
            publicRepositories,
            totalAlerts,
            highSeverityAlerts
        );
        
        return ResponseEntity.ok(stats);
    }
    
    public static class RepositoryStats {
        private final long totalRepositories;
        private final long privateRepositories;
        private final long publicRepositories;
        private final long totalAlerts;
        private final long highSeverityAlerts;
        
        public RepositoryStats(long totalRepositories, long privateRepositories, long publicRepositories,
                              long totalAlerts, long highSeverityAlerts) {
            this.totalRepositories = totalRepositories;
            this.privateRepositories = privateRepositories;
            this.publicRepositories = publicRepositories;
            this.totalAlerts = totalAlerts;
            this.highSeverityAlerts = highSeverityAlerts;
        }
        
        // Getters
        public long getTotalRepositories() { return totalRepositories; }
        public long getPrivateRepositories() { return privateRepositories; }
        public long getPublicRepositories() { return publicRepositories; }
        public long getTotalAlerts() { return totalAlerts; }
        public long getHighSeverityAlerts() { return highSeverityAlerts; }
    }
}
