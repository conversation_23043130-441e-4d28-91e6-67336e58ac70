package com.github.secrets.controller;

import com.github.secrets.enums.AlertStatus;
import com.github.secrets.enums.ScanJobStatus;
import com.github.secrets.enums.Severity;
import com.github.secrets.repository.AlertRepository;
import com.github.secrets.repository.RepositoryRepository;
import com.github.secrets.repository.ScanJobRepository;
import com.github.secrets.service.GitHubApiService;
import com.github.secrets.service.ScanSchedulerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dashboard")
@Tag(name = "Dashboard", description = "APIs for dashboard statistics and overview")
public class DashboardController {
    
    private final RepositoryRepository repositoryRepository;
    private final AlertRepository alertRepository;
    private final ScanJobRepository scanJobRepository;
    private final GitHubApiService gitHubApiService;
    private final ScanSchedulerService scanSchedulerService;
    
    public DashboardController(RepositoryRepository repositoryRepository,
                              AlertRepository alertRepository,
                              ScanJobRepository scanJobRepository,
                              GitHubApiService gitHubApiService,
                              ScanSchedulerService scanSchedulerService) {
        this.repositoryRepository = repositoryRepository;
        this.alertRepository = alertRepository;
        this.scanJobRepository = scanJobRepository;
        this.gitHubApiService = gitHubApiService;
        this.scanSchedulerService = scanSchedulerService;
    }
    
    @GetMapping("/overview")
    @Operation(summary = "Get dashboard overview", description = "Get overall system statistics for dashboard")
    public ResponseEntity<DashboardOverview> getDashboardOverview() {
        // Repository statistics
        long totalRepositories = repositoryRepository.countActiveRepositories();
        long privateRepositories = repositoryRepository.countPrivateRepositories();
        long publicRepositories = repositoryRepository.countPublicRepositories();
        
        // Alert statistics
        long totalAlerts = alertRepository.count();
        long openAlerts = alertRepository.countByStatus(AlertStatus.OPEN);
        long highSeverityAlerts = alertRepository.countBySeverityAndStatus(Severity.HIGH, AlertStatus.OPEN);
        long mediumSeverityAlerts = alertRepository.countBySeverityAndStatus(Severity.MEDIUM, AlertStatus.OPEN);
        long lowSeverityAlerts = alertRepository.countBySeverityAndStatus(Severity.LOW, AlertStatus.OPEN);
        
        // Scan job statistics
        long totalScans = scanJobRepository.count();
        long completedScans = scanJobRepository.countByStatus(ScanJobStatus.COMPLETED);
        long failedScans = scanJobRepository.countByStatus(ScanJobStatus.FAILED);
        long runningScans = scanJobRepository.countByStatus(ScanJobStatus.RUNNING);
        
        // Recent activity (last 24 hours)
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        List<Object> recentAlerts = alertRepository.findRecentAlerts(yesterday);
        List<Object> recentScans = scanJobRepository.findRecentScanJobs(yesterday);
        
        // Rate limit status
        var rateLimitStatus = gitHubApiService.getRateLimitStatus();
        
        // Scanning status
        var scanningStatus = scanSchedulerService.getScanningStatus();
        
        DashboardOverview overview = new DashboardOverview(
            totalRepositories, privateRepositories, publicRepositories,
            totalAlerts, openAlerts, highSeverityAlerts, mediumSeverityAlerts, lowSeverityAlerts,
            totalScans, completedScans, failedScans, runningScans,
            recentAlerts.size(), recentScans.size(),
            rateLimitStatus.getRemaining(), rateLimitStatus.getResetTime(),
            scanningStatus.getActiveScans(), scanningStatus.getMaxConcurrentScans()
        );
        
        return ResponseEntity.ok(overview);
    }
    
    @GetMapping("/alerts/recent")
    @Operation(summary = "Get recent alerts", description = "Get recent alerts for dashboard")
    public ResponseEntity<List<RecentAlert>> getRecentAlerts(@RequestParam(defaultValue = "10") int limit) {
        LocalDateTime since = LocalDateTime.now().minusDays(7); // Last 7 days
        List<Object> alerts = alertRepository.findRecentAlerts(since);
        
        // Convert to DTOs (simplified for dashboard)
        List<RecentAlert> recentAlerts = alerts.stream()
            .limit(limit)
            .map(alert -> {
                // This would need proper casting based on your query result
                return new RecentAlert("Sample Alert", "HIGH", LocalDateTime.now());
            })
            .collect(Collectors.toList());
        
        return ResponseEntity.ok(recentAlerts);
    }
    
    @GetMapping("/trends")
    @Operation(summary = "Get dashboard trends", description = "Get trends data for dashboard charts")
    public ResponseEntity<DashboardTrends> getDashboardTrends(@RequestParam(defaultValue = "30") int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        
        // Get alert trends
        List<Object[]> alertTrends = alertRepository.getAlertTrends(since);
        
        // Get scan trends
        List<Object[]> scanTrends = scanJobRepository.getScanJobTrends(since);
        
        // Get secret type distribution
        List<Object[]> secretTypeStats = alertRepository.getSecretTypeStatistics(AlertStatus.OPEN);
        
        // Get severity distribution
        List<Object[]> severityStats = alertRepository.getSeverityStatistics(AlertStatus.OPEN);
        
        DashboardTrends trends = new DashboardTrends(
            alertTrends, scanTrends, secretTypeStats, severityStats
        );
        
        return ResponseEntity.ok(trends);
    }
    
    @GetMapping("/system-status")
    @Operation(summary = "Get system status", description = "Get current system health and status")
    public ResponseEntity<SystemStatus> getSystemStatus() {
        // Rate limit status
        var rateLimitStatus = gitHubApiService.getRateLimitStatus();
        
        // Scanning status
        var scanningStatus = scanSchedulerService.getScanningStatus();
        
        // Check for stuck jobs
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(2);
        List<Object> stuckJobs = scanJobRepository.findStuckJobs(cutoffTime);
        
        // Average scan duration
        Double avgScanDuration = scanJobRepository.getAverageScanDurationInSeconds();
        
        SystemStatus status = new SystemStatus(
            rateLimitStatus.getRemaining(),
            rateLimitStatus.getResetTime(),
            scanningStatus.getActiveScans(),
            scanningStatus.getMaxConcurrentScans(),
            scanningStatus.getPendingJobs(),
            scanningStatus.getRunningJobs(),
            stuckJobs.size(),
            avgScanDuration != null ? avgScanDuration : 0.0,
            "HEALTHY" // This would be calculated based on various factors
        );
        
        return ResponseEntity.ok(status);
    }
    
    // DTO Classes
    public static class DashboardOverview {
        private final long totalRepositories;
        private final long privateRepositories;
        private final long publicRepositories;
        private final long totalAlerts;
        private final long openAlerts;
        private final long highSeverityAlerts;
        private final long mediumSeverityAlerts;
        private final long lowSeverityAlerts;
        private final long totalScans;
        private final long completedScans;
        private final long failedScans;
        private final long runningScans;
        private final long recentAlerts;
        private final long recentScans;
        private final int rateLimitRemaining;
        private final LocalDateTime rateLimitReset;
        private final int activeScans;
        private final int maxConcurrentScans;
        
        public DashboardOverview(long totalRepositories, long privateRepositories, long publicRepositories,
                               long totalAlerts, long openAlerts, long highSeverityAlerts, long mediumSeverityAlerts,
                               long lowSeverityAlerts, long totalScans, long completedScans, long failedScans,
                               long runningScans, long recentAlerts, long recentScans, int rateLimitRemaining,
                               LocalDateTime rateLimitReset, int activeScans, int maxConcurrentScans) {
            this.totalRepositories = totalRepositories;
            this.privateRepositories = privateRepositories;
            this.publicRepositories = publicRepositories;
            this.totalAlerts = totalAlerts;
            this.openAlerts = openAlerts;
            this.highSeverityAlerts = highSeverityAlerts;
            this.mediumSeverityAlerts = mediumSeverityAlerts;
            this.lowSeverityAlerts = lowSeverityAlerts;
            this.totalScans = totalScans;
            this.completedScans = completedScans;
            this.failedScans = failedScans;
            this.runningScans = runningScans;
            this.recentAlerts = recentAlerts;
            this.recentScans = recentScans;
            this.rateLimitRemaining = rateLimitRemaining;
            this.rateLimitReset = rateLimitReset;
            this.activeScans = activeScans;
            this.maxConcurrentScans = maxConcurrentScans;
        }
        
        // Getters
        public long getTotalRepositories() { return totalRepositories; }
        public long getPrivateRepositories() { return privateRepositories; }
        public long getPublicRepositories() { return publicRepositories; }
        public long getTotalAlerts() { return totalAlerts; }
        public long getOpenAlerts() { return openAlerts; }
        public long getHighSeverityAlerts() { return highSeverityAlerts; }
        public long getMediumSeverityAlerts() { return mediumSeverityAlerts; }
        public long getLowSeverityAlerts() { return lowSeverityAlerts; }
        public long getTotalScans() { return totalScans; }
        public long getCompletedScans() { return completedScans; }
        public long getFailedScans() { return failedScans; }
        public long getRunningScans() { return runningScans; }
        public long getRecentAlerts() { return recentAlerts; }
        public long getRecentScans() { return recentScans; }
        public int getRateLimitRemaining() { return rateLimitRemaining; }
        public LocalDateTime getRateLimitReset() { return rateLimitReset; }
        public int getActiveScans() { return activeScans; }
        public int getMaxConcurrentScans() { return maxConcurrentScans; }
    }
    
    public static class RecentAlert {
        private final String description;
        private final String severity;
        private final LocalDateTime createdAt;
        
        public RecentAlert(String description, String severity, LocalDateTime createdAt) {
            this.description = description;
            this.severity = severity;
            this.createdAt = createdAt;
        }
        
        public String getDescription() { return description; }
        public String getSeverity() { return severity; }
        public LocalDateTime getCreatedAt() { return createdAt; }
    }
    
    public static class DashboardTrends {
        private final List<Object[]> alertTrends;
        private final List<Object[]> scanTrends;
        private final List<Object[]> secretTypeStats;
        private final List<Object[]> severityStats;
        
        public DashboardTrends(List<Object[]> alertTrends, List<Object[]> scanTrends,
                             List<Object[]> secretTypeStats, List<Object[]> severityStats) {
            this.alertTrends = alertTrends;
            this.scanTrends = scanTrends;
            this.secretTypeStats = secretTypeStats;
            this.severityStats = severityStats;
        }
        
        public List<Object[]> getAlertTrends() { return alertTrends; }
        public List<Object[]> getScanTrends() { return scanTrends; }
        public List<Object[]> getSecretTypeStats() { return secretTypeStats; }
        public List<Object[]> getSeverityStats() { return severityStats; }
    }
    
    public static class SystemStatus {
        private final int rateLimitRemaining;
        private final LocalDateTime rateLimitReset;
        private final int activeScans;
        private final int maxConcurrentScans;
        private final long pendingJobs;
        private final long runningJobs;
        private final long stuckJobs;
        private final double avgScanDuration;
        private final String healthStatus;
        
        public SystemStatus(int rateLimitRemaining, LocalDateTime rateLimitReset, int activeScans,
                          int maxConcurrentScans, long pendingJobs, long runningJobs, long stuckJobs,
                          double avgScanDuration, String healthStatus) {
            this.rateLimitRemaining = rateLimitRemaining;
            this.rateLimitReset = rateLimitReset;
            this.activeScans = activeScans;
            this.maxConcurrentScans = maxConcurrentScans;
            this.pendingJobs = pendingJobs;
            this.runningJobs = runningJobs;
            this.stuckJobs = stuckJobs;
            this.avgScanDuration = avgScanDuration;
            this.healthStatus = healthStatus;
        }
        
        // Getters
        public int getRateLimitRemaining() { return rateLimitRemaining; }
        public LocalDateTime getRateLimitReset() { return rateLimitReset; }
        public int getActiveScans() { return activeScans; }
        public int getMaxConcurrentScans() { return maxConcurrentScans; }
        public long getPendingJobs() { return pendingJobs; }
        public long getRunningJobs() { return runningJobs; }
        public long getStuckJobs() { return stuckJobs; }
        public double getAvgScanDuration() { return avgScanDuration; }
        public String getHealthStatus() { return healthStatus; }
    }
}
