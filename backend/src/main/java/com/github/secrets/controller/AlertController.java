package com.github.secrets.controller;

import com.github.secrets.dto.AlertDto;
import com.github.secrets.entity.Alert;
import com.github.secrets.entity.User;
import com.github.secrets.enums.AlertStatus;
import com.github.secrets.enums.Severity;
import com.github.secrets.repository.AlertRepository;
import com.github.secrets.repository.UserRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/alerts")
@Tag(name = "Alert Management", description = "APIs for managing security alerts")
public class AlertController {
    
    private final AlertRepository alertRepository;
    private final UserRepository userRepository;
    
    public AlertController(AlertRepository alertRepository, UserRepository userRepository) {
        this.alertRepository = alertRepository;
        this.userRepository = userRepository;
    }
    
    @GetMapping
    @Operation(summary = "Get all alerts", description = "Retrieve all alerts with pagination and filtering")
    public ResponseEntity<Page<AlertDto>> getAllAlerts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) AlertStatus status,
            @RequestParam(required = false) Severity severity,
            @RequestParam(required = false) Long repositoryId,
            @RequestParam(required = false) String search) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<Alert> alerts;
        
        if (search != null && !search.trim().isEmpty()) {
            alerts = alertRepository.searchAlerts(search.trim(), pageable);
        } else if (repositoryId != null && status != null) {
            alerts = alertRepository.findByRepositoryIdAndStatus(repositoryId, status, pageable);
        } else if (severity != null && status != null) {
            alerts = alertRepository.findBySeverityAndStatusOrderByCreatedAtDesc(severity, status, pageable);
        } else if (status != null) {
            alerts = alertRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
        } else {
            alerts = alertRepository.findAll(pageable);
        }
        
        Page<AlertDto> alertDtos = alerts.map(AlertDto::new);
        return ResponseEntity.ok(alertDtos);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get alert by ID", description = "Retrieve a specific alert by its ID")
    public ResponseEntity<AlertDto> getAlert(@PathVariable Long id) {
        Optional<Alert> alert = alertRepository.findById(id);
        
        if (alert.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(new AlertDto(alert.get()));
    }
    
    @PutMapping("/{id}/acknowledge")
    @Operation(summary = "Acknowledge alert", description = "Mark an alert as acknowledged")
    public ResponseEntity<AlertDto> acknowledgeAlert(@PathVariable Long id,
                                                    @RequestBody(required = false) String notes,
                                                    Authentication authentication) {
        Optional<Alert> alertOpt = alertRepository.findById(id);
        
        if (alertOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        Optional<User> user = userRepository.findByUsername(authentication.getName());
        if (user.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        Alert alert = alertOpt.get();
        alert.setStatus(AlertStatus.ACKNOWLEDGED);
        alert.setAcknowledgedByUser(user.get());
        alert.setAcknowledgedAt(LocalDateTime.now());
        if (notes != null) {
            alert.setResolutionNotes(notes);
        }
        
        alert = alertRepository.save(alert);
        
        return ResponseEntity.ok(new AlertDto(alert));
    }
    
    @PutMapping("/{id}/resolve")
    @Operation(summary = "Resolve alert", description = "Mark an alert as resolved")
    public ResponseEntity<AlertDto> resolveAlert(@PathVariable Long id,
                                               @Valid @RequestBody ResolveAlertRequest request,
                                               Authentication authentication) {
        Optional<Alert> alertOpt = alertRepository.findById(id);
        
        if (alertOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        Optional<User> user = userRepository.findByUsername(authentication.getName());
        if (user.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        Alert alert = alertOpt.get();
        alert.setStatus(AlertStatus.RESOLVED);
        alert.setAcknowledgedByUser(user.get());
        alert.setAcknowledgedAt(LocalDateTime.now());
        alert.setResolutionNotes(request.getNotes());
        
        alert = alertRepository.save(alert);
        
        return ResponseEntity.ok(new AlertDto(alert));
    }
    
    @PutMapping("/{id}/false-positive")
    @Operation(summary = "Mark as false positive", description = "Mark an alert as a false positive")
    public ResponseEntity<AlertDto> markAsFalsePositive(@PathVariable Long id,
                                                       @RequestBody(required = false) String notes,
                                                       Authentication authentication) {
        Optional<Alert> alertOpt = alertRepository.findById(id);
        
        if (alertOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        Optional<User> user = userRepository.findByUsername(authentication.getName());
        if (user.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        Alert alert = alertOpt.get();
        alert.setStatus(AlertStatus.FALSE_POSITIVE);
        alert.setAcknowledgedByUser(user.get());
        alert.setAcknowledgedAt(LocalDateTime.now());
        if (notes != null) {
            alert.setResolutionNotes(notes);
        }
        
        alert = alertRepository.save(alert);
        
        return ResponseEntity.ok(new AlertDto(alert));
    }
    
    @GetMapping("/stats")
    @Operation(summary = "Get alert statistics", description = "Get overall alert statistics")
    public ResponseEntity<AlertStats> getAlertStats() {
        long totalAlerts = alertRepository.count();
        long openAlerts = alertRepository.countByStatus(AlertStatus.OPEN);
        long acknowledgedAlerts = alertRepository.countByStatus(AlertStatus.ACKNOWLEDGED);
        long resolvedAlerts = alertRepository.countByStatus(AlertStatus.RESOLVED);
        long falsePositives = alertRepository.countByStatus(AlertStatus.FALSE_POSITIVE);
        
        long highSeverityAlerts = alertRepository.countBySeverityAndStatus(Severity.HIGH, AlertStatus.OPEN);
        long mediumSeverityAlerts = alertRepository.countBySeverityAndStatus(Severity.MEDIUM, AlertStatus.OPEN);
        long lowSeverityAlerts = alertRepository.countBySeverityAndStatus(Severity.LOW, AlertStatus.OPEN);
        
        // Get recent alerts (last 24 hours)
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        List<Alert> recentAlerts = alertRepository.findRecentAlerts(yesterday);
        
        // Get secret type statistics
        List<Object[]> secretTypeStats = alertRepository.getSecretTypeStatistics(AlertStatus.OPEN);
        
        AlertStats stats = new AlertStats(
            totalAlerts, openAlerts, acknowledgedAlerts, resolvedAlerts, falsePositives,
            highSeverityAlerts, mediumSeverityAlerts, lowSeverityAlerts,
            recentAlerts.size(), secretTypeStats
        );
        
        return ResponseEntity.ok(stats);
    }
    
    @GetMapping("/trends")
    @Operation(summary = "Get alert trends", description = "Get alert trends over time")
    public ResponseEntity<List<AlertTrend>> getAlertTrends(@RequestParam(defaultValue = "30") int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        List<Object[]> trends = alertRepository.getAlertTrends(since);
        
        List<AlertTrend> alertTrends = trends.stream()
            .map(trend -> new AlertTrend((java.sql.Date) trend[0], ((Number) trend[1]).longValue()))
            .collect(Collectors.toList());
        
        return ResponseEntity.ok(alertTrends);
    }
    
    public static class ResolveAlertRequest {
        private String notes;
        
        public String getNotes() {
            return notes;
        }
        
        public void setNotes(String notes) {
            this.notes = notes;
        }
    }
    
    public static class AlertStats {
        private final long totalAlerts;
        private final long openAlerts;
        private final long acknowledgedAlerts;
        private final long resolvedAlerts;
        private final long falsePositives;
        private final long highSeverityAlerts;
        private final long mediumSeverityAlerts;
        private final long lowSeverityAlerts;
        private final long recentAlerts;
        private final List<Object[]> secretTypeStats;
        
        public AlertStats(long totalAlerts, long openAlerts, long acknowledgedAlerts, long resolvedAlerts,
                         long falsePositives, long highSeverityAlerts, long mediumSeverityAlerts,
                         long lowSeverityAlerts, long recentAlerts, List<Object[]> secretTypeStats) {
            this.totalAlerts = totalAlerts;
            this.openAlerts = openAlerts;
            this.acknowledgedAlerts = acknowledgedAlerts;
            this.resolvedAlerts = resolvedAlerts;
            this.falsePositives = falsePositives;
            this.highSeverityAlerts = highSeverityAlerts;
            this.mediumSeverityAlerts = mediumSeverityAlerts;
            this.lowSeverityAlerts = lowSeverityAlerts;
            this.recentAlerts = recentAlerts;
            this.secretTypeStats = secretTypeStats;
        }
        
        // Getters
        public long getTotalAlerts() { return totalAlerts; }
        public long getOpenAlerts() { return openAlerts; }
        public long getAcknowledgedAlerts() { return acknowledgedAlerts; }
        public long getResolvedAlerts() { return resolvedAlerts; }
        public long getFalsePositives() { return falsePositives; }
        public long getHighSeverityAlerts() { return highSeverityAlerts; }
        public long getMediumSeverityAlerts() { return mediumSeverityAlerts; }
        public long getLowSeverityAlerts() { return lowSeverityAlerts; }
        public long getRecentAlerts() { return recentAlerts; }
        public List<Object[]> getSecretTypeStats() { return secretTypeStats; }
    }
    
    public static class AlertTrend {
        private final java.sql.Date date;
        private final long count;
        
        public AlertTrend(java.sql.Date date, long count) {
            this.date = date;
            this.count = count;
        }
        
        public java.sql.Date getDate() { return date; }
        public long getCount() { return count; }
    }
}
