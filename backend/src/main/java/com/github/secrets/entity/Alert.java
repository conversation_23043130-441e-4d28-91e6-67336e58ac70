package com.github.secrets.entity;

import com.github.secrets.enums.AlertStatus;
import com.github.secrets.enums.Severity;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "alerts")
@EntityListeners(AuditingEntityListener.class)
public class Alert {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "repository_id", nullable = false)
    private Repository repository;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "scan_job_id")
    private ScanJob scanJob;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "secret_pattern_id")
    private SecretPattern secretPattern;
    
    @NotBlank
    @Column(name = "file_path", nullable = false, length = 1000)
    private String filePath;
    
    @NotNull
    @Column(name = "line_number", nullable = false)
    private Integer lineNumber;
    
    @NotBlank
    @Column(name = "commit_sha", nullable = false, length = 40)
    private String commitSha;
    
    @NotBlank
    @Column(name = "secret_type", nullable = false, length = 100)
    private String secretType;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private Severity severity;
    
    @NotNull
    @DecimalMin("0.00")
    @DecimalMax("1.00")
    @Column(name = "confidence_score", nullable = false, precision = 3, scale = 2)
    private BigDecimal confidenceScore;
    
    @Column(name = "partial_secret", length = 20)
    private String partialSecret; // First and last 4 chars for reference
    
    @Column(name = "context_before", columnDefinition = "TEXT")
    private String contextBefore;
    
    @Column(name = "context_after", columnDefinition = "TEXT")
    private String contextAfter;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 50)
    private AlertStatus status = AlertStatus.OPEN;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "acknowledged_by_user_id")
    private User acknowledgedByUser;
    
    @Column(name = "acknowledged_at")
    private LocalDateTime acknowledgedAt;
    
    @Column(name = "resolution_notes", columnDefinition = "TEXT")
    private String resolutionNotes;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Alert() {}
    
    public Alert(Repository repository, String filePath, Integer lineNumber, String commitSha,
                 String secretType, Severity severity, BigDecimal confidenceScore) {
        this.repository = repository;
        this.filePath = filePath;
        this.lineNumber = lineNumber;
        this.commitSha = commitSha;
        this.secretType = secretType;
        this.severity = severity;
        this.confidenceScore = confidenceScore;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Repository getRepository() {
        return repository;
    }
    
    public void setRepository(Repository repository) {
        this.repository = repository;
    }
    
    public ScanJob getScanJob() {
        return scanJob;
    }
    
    public void setScanJob(ScanJob scanJob) {
        this.scanJob = scanJob;
    }
    
    public SecretPattern getSecretPattern() {
        return secretPattern;
    }
    
    public void setSecretPattern(SecretPattern secretPattern) {
        this.secretPattern = secretPattern;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public Integer getLineNumber() {
        return lineNumber;
    }
    
    public void setLineNumber(Integer lineNumber) {
        this.lineNumber = lineNumber;
    }
    
    public String getCommitSha() {
        return commitSha;
    }
    
    public void setCommitSha(String commitSha) {
        this.commitSha = commitSha;
    }
    
    public String getSecretType() {
        return secretType;
    }
    
    public void setSecretType(String secretType) {
        this.secretType = secretType;
    }
    
    public Severity getSeverity() {
        return severity;
    }
    
    public void setSeverity(Severity severity) {
        this.severity = severity;
    }
    
    public BigDecimal getConfidenceScore() {
        return confidenceScore;
    }
    
    public void setConfidenceScore(BigDecimal confidenceScore) {
        this.confidenceScore = confidenceScore;
    }
    
    public String getPartialSecret() {
        return partialSecret;
    }
    
    public void setPartialSecret(String partialSecret) {
        this.partialSecret = partialSecret;
    }
    
    public String getContextBefore() {
        return contextBefore;
    }
    
    public void setContextBefore(String contextBefore) {
        this.contextBefore = contextBefore;
    }
    
    public String getContextAfter() {
        return contextAfter;
    }
    
    public void setContextAfter(String contextAfter) {
        this.contextAfter = contextAfter;
    }
    
    public AlertStatus getStatus() {
        return status;
    }
    
    public void setStatus(AlertStatus status) {
        this.status = status;
    }
    
    public User getAcknowledgedByUser() {
        return acknowledgedByUser;
    }
    
    public void setAcknowledgedByUser(User acknowledgedByUser) {
        this.acknowledgedByUser = acknowledgedByUser;
    }
    
    public LocalDateTime getAcknowledgedAt() {
        return acknowledgedAt;
    }
    
    public void setAcknowledgedAt(LocalDateTime acknowledgedAt) {
        this.acknowledgedAt = acknowledgedAt;
    }
    
    public String getResolutionNotes() {
        return resolutionNotes;
    }
    
    public void setResolutionNotes(String resolutionNotes) {
        this.resolutionNotes = resolutionNotes;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Alert)) return false;
        Alert alert = (Alert) o;
        return id != null && id.equals(alert.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
    
    @Override
    public String toString() {
        return "Alert{" +
                "id=" + id +
                ", filePath='" + filePath + '\'' +
                ", lineNumber=" + lineNumber +
                ", secretType='" + secretType + '\'' +
                ", severity=" + severity +
                ", status=" + status +
                '}';
    }
}
