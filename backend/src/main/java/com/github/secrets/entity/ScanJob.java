package com.github.secrets.entity;

import com.github.secrets.enums.ScanJobStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "scan_jobs")
@EntityListeners(AuditingEntityListener.class)
public class ScanJob {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "repository_id", nullable = false)
    private Repository repository;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 50)
    private ScanJobStatus status = ScanJobStatus.PENDING;
    
    @Column(name = "scan_type", length = 50)
    private String scanType = "SCHEDULED"; // SCHEDULED, MANUAL, WEBHOOK
    
    @Column(name = "commit_sha", length = 40)
    private String commitSha;
    
    @Column(name = "started_at")
    private LocalDateTime startedAt;
    
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    @Column(name = "files_scanned")
    private Integer filesScanned = 0;
    
    @Column(name = "secrets_found")
    private Integer secretsFound = 0;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @OneToMany(mappedBy = "scanJob", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Alert> alerts = new HashSet<>();
    
    // Constructors
    public ScanJob() {}
    
    public ScanJob(Repository repository, String scanType) {
        this.repository = repository;
        this.scanType = scanType;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Repository getRepository() {
        return repository;
    }
    
    public void setRepository(Repository repository) {
        this.repository = repository;
    }
    
    public ScanJobStatus getStatus() {
        return status;
    }
    
    public void setStatus(ScanJobStatus status) {
        this.status = status;
    }
    
    public String getScanType() {
        return scanType;
    }
    
    public void setScanType(String scanType) {
        this.scanType = scanType;
    }
    
    public String getCommitSha() {
        return commitSha;
    }
    
    public void setCommitSha(String commitSha) {
        this.commitSha = commitSha;
    }
    
    public LocalDateTime getStartedAt() {
        return startedAt;
    }
    
    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }
    
    public LocalDateTime getCompletedAt() {
        return completedAt;
    }
    
    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }
    
    public Integer getFilesScanned() {
        return filesScanned;
    }
    
    public void setFilesScanned(Integer filesScanned) {
        this.filesScanned = filesScanned;
    }
    
    public Integer getSecretsFound() {
        return secretsFound;
    }
    
    public void setSecretsFound(Integer secretsFound) {
        this.secretsFound = secretsFound;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public Set<Alert> getAlerts() {
        return alerts;
    }
    
    public void setAlerts(Set<Alert> alerts) {
        this.alerts = alerts;
    }
    
    public void markAsStarted() {
        this.status = ScanJobStatus.RUNNING;
        this.startedAt = LocalDateTime.now();
    }
    
    public void markAsCompleted() {
        this.status = ScanJobStatus.COMPLETED;
        this.completedAt = LocalDateTime.now();
    }
    
    public void markAsFailed(String errorMessage) {
        this.status = ScanJobStatus.FAILED;
        this.completedAt = LocalDateTime.now();
        this.errorMessage = errorMessage;
    }
    
    public long getDurationInSeconds() {
        if (startedAt != null && completedAt != null) {
            return java.time.Duration.between(startedAt, completedAt).getSeconds();
        }
        return 0;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ScanJob)) return false;
        ScanJob scanJob = (ScanJob) o;
        return id != null && id.equals(scanJob.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
    
    @Override
    public String toString() {
        return "ScanJob{" +
                "id=" + id +
                ", status=" + status +
                ", scanType='" + scanType + '\'' +
                ", filesScanned=" + filesScanned +
                ", secretsFound=" + secretsFound +
                '}';
    }
}
