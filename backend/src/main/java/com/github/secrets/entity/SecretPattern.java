package com.github.secrets.entity;

import com.github.secrets.enums.Severity;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "secret_patterns")
@EntityListeners(AuditingEntityListener.class)
public class SecretPattern {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(unique = true, nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @NotBlank
    @Column(name = "pattern_regex", nullable = false, columnDefinition = "TEXT")
    private String patternRegex;
    
    @NotBlank
    @Column(name = "secret_type", nullable = false, length = 100)
    private String secretType;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private Severity severity = Severity.MEDIUM;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @DecimalMin("0.00")
    @DecimalMax("1.00")
    @Column(name = "entropy_threshold", precision = 3, scale = 2)
    private BigDecimal entropyThreshold;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "secretPattern", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Alert> alerts = new HashSet<>();
    
    // Constructors
    public SecretPattern() {}
    
    public SecretPattern(String name, String description, String patternRegex, String secretType, Severity severity) {
        this.name = name;
        this.description = description;
        this.patternRegex = patternRegex;
        this.secretType = secretType;
        this.severity = severity;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getPatternRegex() {
        return patternRegex;
    }
    
    public void setPatternRegex(String patternRegex) {
        this.patternRegex = patternRegex;
    }
    
    public String getSecretType() {
        return secretType;
    }
    
    public void setSecretType(String secretType) {
        this.secretType = secretType;
    }
    
    public Severity getSeverity() {
        return severity;
    }
    
    public void setSeverity(Severity severity) {
        this.severity = severity;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public BigDecimal getEntropyThreshold() {
        return entropyThreshold;
    }
    
    public void setEntropyThreshold(BigDecimal entropyThreshold) {
        this.entropyThreshold = entropyThreshold;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Set<Alert> getAlerts() {
        return alerts;
    }
    
    public void setAlerts(Set<Alert> alerts) {
        this.alerts = alerts;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SecretPattern)) return false;
        SecretPattern that = (SecretPattern) o;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
    
    @Override
    public String toString() {
        return "SecretPattern{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", secretType='" + secretType + '\'' +
                ", severity=" + severity +
                ", isActive=" + isActive +
                '}';
    }
}
