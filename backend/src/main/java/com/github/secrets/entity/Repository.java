package com.github.secrets.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "repositories")
@EntityListeners(AuditingEntityListener.class)
public class Repository {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(nullable = false)
    private String name;
    
    @NotBlank
    @Column(name = "full_name", unique = true, nullable = false)
    private String fullName; // owner/repo-name
    
    @NotBlank
    @Column(nullable = false)
    private String owner;
    
    @NotNull
    @Column(name = "github_id", unique = true, nullable = false)
    private Long githubId;
    
    @NotBlank
    @Column(name = "clone_url", nullable = false, length = 500)
    private String cloneUrl;
    
    @Column(name = "default_branch")
    private String defaultBranch = "main";
    
    @Column(name = "is_private")
    private Boolean isPrivate = false;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "last_scan_at")
    private LocalDateTime lastScanAt;
    
    @Column(name = "last_commit_sha", length = 40)
    private String lastCommitSha;
    
    @Column(name = "scan_frequency_minutes")
    private Integer scanFrequencyMinutes = 15;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "added_by_user_id")
    private User addedByUser;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "repository", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Alert> alerts = new HashSet<>();
    
    @OneToMany(mappedBy = "repository", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<ScanJob> scanJobs = new HashSet<>();
    
    // Constructors
    public Repository() {}
    
    public Repository(String name, String fullName, String owner, Long githubId, String cloneUrl) {
        this.name = name;
        this.fullName = fullName;
        this.owner = owner;
        this.githubId = githubId;
        this.cloneUrl = cloneUrl;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public String getOwner() {
        return owner;
    }
    
    public void setOwner(String owner) {
        this.owner = owner;
    }
    
    public Long getGithubId() {
        return githubId;
    }
    
    public void setGithubId(Long githubId) {
        this.githubId = githubId;
    }
    
    public String getCloneUrl() {
        return cloneUrl;
    }
    
    public void setCloneUrl(String cloneUrl) {
        this.cloneUrl = cloneUrl;
    }
    
    public String getDefaultBranch() {
        return defaultBranch;
    }
    
    public void setDefaultBranch(String defaultBranch) {
        this.defaultBranch = defaultBranch;
    }
    
    public Boolean getIsPrivate() {
        return isPrivate;
    }
    
    public void setIsPrivate(Boolean isPrivate) {
        this.isPrivate = isPrivate;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public LocalDateTime getLastScanAt() {
        return lastScanAt;
    }
    
    public void setLastScanAt(LocalDateTime lastScanAt) {
        this.lastScanAt = lastScanAt;
    }
    
    public String getLastCommitSha() {
        return lastCommitSha;
    }
    
    public void setLastCommitSha(String lastCommitSha) {
        this.lastCommitSha = lastCommitSha;
    }
    
    public Integer getScanFrequencyMinutes() {
        return scanFrequencyMinutes;
    }
    
    public void setScanFrequencyMinutes(Integer scanFrequencyMinutes) {
        this.scanFrequencyMinutes = scanFrequencyMinutes;
    }
    
    public User getAddedByUser() {
        return addedByUser;
    }
    
    public void setAddedByUser(User addedByUser) {
        this.addedByUser = addedByUser;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Set<Alert> getAlerts() {
        return alerts;
    }
    
    public void setAlerts(Set<Alert> alerts) {
        this.alerts = alerts;
    }
    
    public Set<ScanJob> getScanJobs() {
        return scanJobs;
    }
    
    public void setScanJobs(Set<ScanJob> scanJobs) {
        this.scanJobs = scanJobs;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Repository)) return false;
        Repository that = (Repository) o;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
    
    @Override
    public String toString() {
        return "Repository{" +
                "id=" + id +
                ", fullName='" + fullName + '\'' +
                ", owner='" + owner + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
