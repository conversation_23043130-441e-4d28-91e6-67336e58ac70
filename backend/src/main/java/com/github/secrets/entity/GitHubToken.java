package com.github.secrets.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Entity
@Table(name = "github_tokens")
@EntityListeners(AuditingEntityListener.class)
public class GitHubToken {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @NotBlank
    @Column(name = "token_name", nullable = false)
    private String tokenName;
    
    @NotBlank
    @Column(name = "encrypted_token", nullable = false, columnDefinition = "TEXT")
    private String encryptedToken;
    
    @Column(columnDefinition = "TEXT[]")
    private String[] scopes;
    
    @Column(name = "rate_limit_remaining")
    private Integer rateLimitRemaining;
    
    @Column(name = "rate_limit_reset_at")
    private LocalDateTime rateLimitResetAt;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public GitHubToken() {}
    
    public GitHubToken(User user, String tokenName, String encryptedToken) {
        this.user = user;
        this.tokenName = tokenName;
        this.encryptedToken = encryptedToken;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public String getTokenName() {
        return tokenName;
    }
    
    public void setTokenName(String tokenName) {
        this.tokenName = tokenName;
    }
    
    public String getEncryptedToken() {
        return encryptedToken;
    }
    
    public void setEncryptedToken(String encryptedToken) {
        this.encryptedToken = encryptedToken;
    }
    
    public String[] getScopes() {
        return scopes;
    }
    
    public void setScopes(String[] scopes) {
        this.scopes = scopes;
    }
    
    public Integer getRateLimitRemaining() {
        return rateLimitRemaining;
    }
    
    public void setRateLimitRemaining(Integer rateLimitRemaining) {
        this.rateLimitRemaining = rateLimitRemaining;
    }
    
    public LocalDateTime getRateLimitResetAt() {
        return rateLimitResetAt;
    }
    
    public void setRateLimitResetAt(LocalDateTime rateLimitResetAt) {
        this.rateLimitResetAt = rateLimitResetAt;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof GitHubToken)) return false;
        GitHubToken that = (GitHubToken) o;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
    
    @Override
    public String toString() {
        return "GitHubToken{" +
                "id=" + id +
                ", tokenName='" + tokenName + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
