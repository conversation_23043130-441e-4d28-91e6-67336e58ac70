package com.github.secrets.dto.github;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GitHubRepository {
    
    private Long id;
    private String name;
    
    @JsonProperty("full_name")
    private String fullName;
    
    private GitHubOwner owner;
    
    @JsonProperty("clone_url")
    private String cloneUrl;
    
    @JsonProperty("default_branch")
    private String defaultBranch;
    
    @JsonProperty("private")
    private Boolean isPrivate;
    
    private String description;
    private String language;
    
    @JsonProperty("stargazers_count")
    private Integer stargazersCount;
    
    @JsonProperty("forks_count")
    private Integer forksCount;
    
    @JsonProperty("updated_at")
    private String updatedAt;
    
    @JsonProperty("pushed_at")
    private String pushedAt;
    
    // Constructors
    public GitHubRepository() {}
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public GitHubOwner getOwner() {
        return owner;
    }
    
    public void setOwner(GitHubOwner owner) {
        this.owner = owner;
    }
    
    public String getCloneUrl() {
        return cloneUrl;
    }
    
    public void setCloneUrl(String cloneUrl) {
        this.cloneUrl = cloneUrl;
    }
    
    public String getDefaultBranch() {
        return defaultBranch;
    }
    
    public void setDefaultBranch(String defaultBranch) {
        this.defaultBranch = defaultBranch;
    }
    
    public Boolean getIsPrivate() {
        return isPrivate;
    }
    
    public void setIsPrivate(Boolean isPrivate) {
        this.isPrivate = isPrivate;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getLanguage() {
        return language;
    }
    
    public void setLanguage(String language) {
        this.language = language;
    }
    
    public Integer getStargazersCount() {
        return stargazersCount;
    }
    
    public void setStargazersCount(Integer stargazersCount) {
        this.stargazersCount = stargazersCount;
    }
    
    public Integer getForksCount() {
        return forksCount;
    }
    
    public void setForksCount(Integer forksCount) {
        this.forksCount = forksCount;
    }
    
    public String getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getPushedAt() {
        return pushedAt;
    }
    
    public void setPushedAt(String pushedAt) {
        this.pushedAt = pushedAt;
    }
    
    @Override
    public String toString() {
        return "GitHubRepository{" +
                "id=" + id +
                ", fullName='" + fullName + '\'' +
                ", isPrivate=" + isPrivate +
                '}';
    }
}
