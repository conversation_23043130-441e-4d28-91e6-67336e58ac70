package com.github.secrets.dto;

import com.github.secrets.entity.Repository;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

public class RepositoryDto {
    
    private Long id;
    
    @NotBlank(message = "Repository name is required")
    private String name;
    
    @NotBlank(message = "Full name is required")
    private String fullName;
    
    @NotBlank(message = "Owner is required")
    private String owner;
    
    @NotNull(message = "GitHub ID is required")
    private Long githubId;
    
    @NotBlank(message = "Clone URL is required")
    private String cloneUrl;
    
    private String defaultBranch;
    private Boolean isPrivate;
    private Boolean isActive;
    private LocalDateTime lastScanAt;
    private String lastCommitSha;
    private Integer scanFrequencyMinutes;
    private String addedByUsername;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Statistics
    private Long totalAlerts;
    private Long openAlerts;
    private Long highSeverityAlerts;
    
    // Constructors
    public RepositoryDto() {}
    
    public RepositoryDto(Repository repository) {
        this.id = repository.getId();
        this.name = repository.getName();
        this.fullName = repository.getFullName();
        this.owner = repository.getOwner();
        this.githubId = repository.getGithubId();
        this.cloneUrl = repository.getCloneUrl();
        this.defaultBranch = repository.getDefaultBranch();
        this.isPrivate = repository.getIsPrivate();
        this.isActive = repository.getIsActive();
        this.lastScanAt = repository.getLastScanAt();
        this.lastCommitSha = repository.getLastCommitSha();
        this.scanFrequencyMinutes = repository.getScanFrequencyMinutes();
        this.createdAt = repository.getCreatedAt();
        this.updatedAt = repository.getUpdatedAt();
        
        if (repository.getAddedByUser() != null) {
            this.addedByUsername = repository.getAddedByUser().getUsername();
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public String getOwner() {
        return owner;
    }
    
    public void setOwner(String owner) {
        this.owner = owner;
    }
    
    public Long getGithubId() {
        return githubId;
    }
    
    public void setGithubId(Long githubId) {
        this.githubId = githubId;
    }
    
    public String getCloneUrl() {
        return cloneUrl;
    }
    
    public void setCloneUrl(String cloneUrl) {
        this.cloneUrl = cloneUrl;
    }
    
    public String getDefaultBranch() {
        return defaultBranch;
    }
    
    public void setDefaultBranch(String defaultBranch) {
        this.defaultBranch = defaultBranch;
    }
    
    public Boolean getIsPrivate() {
        return isPrivate;
    }
    
    public void setIsPrivate(Boolean isPrivate) {
        this.isPrivate = isPrivate;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public LocalDateTime getLastScanAt() {
        return lastScanAt;
    }
    
    public void setLastScanAt(LocalDateTime lastScanAt) {
        this.lastScanAt = lastScanAt;
    }
    
    public String getLastCommitSha() {
        return lastCommitSha;
    }
    
    public void setLastCommitSha(String lastCommitSha) {
        this.lastCommitSha = lastCommitSha;
    }
    
    public Integer getScanFrequencyMinutes() {
        return scanFrequencyMinutes;
    }
    
    public void setScanFrequencyMinutes(Integer scanFrequencyMinutes) {
        this.scanFrequencyMinutes = scanFrequencyMinutes;
    }
    
    public String getAddedByUsername() {
        return addedByUsername;
    }
    
    public void setAddedByUsername(String addedByUsername) {
        this.addedByUsername = addedByUsername;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Long getTotalAlerts() {
        return totalAlerts;
    }
    
    public void setTotalAlerts(Long totalAlerts) {
        this.totalAlerts = totalAlerts;
    }
    
    public Long getOpenAlerts() {
        return openAlerts;
    }
    
    public void setOpenAlerts(Long openAlerts) {
        this.openAlerts = openAlerts;
    }
    
    public Long getHighSeverityAlerts() {
        return highSeverityAlerts;
    }
    
    public void setHighSeverityAlerts(Long highSeverityAlerts) {
        this.highSeverityAlerts = highSeverityAlerts;
    }
}
