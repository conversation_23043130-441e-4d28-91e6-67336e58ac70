package com.github.secrets.dto.github;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GitHubOwner {
    
    private Long id;
    private String login;
    
    @JsonProperty("avatar_url")
    private String avatarUrl;
    
    private String type; // User or Organization
    
    @JsonProperty("html_url")
    private String htmlUrl;
    
    // Constructors
    public GitHubOwner() {}
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getLogin() {
        return login;
    }
    
    public void setLogin(String login) {
        this.login = login;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getHtmlUrl() {
        return htmlUrl;
    }
    
    public void setHtmlUrl(String htmlUrl) {
        this.htmlUrl = htmlUrl;
    }
    
    @Override
    public String toString() {
        return "GitHubOwner{" +
                "id=" + id +
                ", login='" + login + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
