package com.github.secrets.dto;

import com.github.secrets.entity.Alert;
import com.github.secrets.enums.AlertStatus;
import com.github.secrets.enums.Severity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AlertDto {
    
    private Long id;
    private Long repositoryId;
    private String repositoryName;
    private String repositoryFullName;
    private String filePath;
    private Integer lineNumber;
    private String commitSha;
    private String secretType;
    private Severity severity;
    private BigDecimal confidenceScore;
    private String partialSecret;
    private String contextBefore;
    private String contextAfter;
    private AlertStatus status;
    private String acknowledgedByUsername;
    private LocalDateTime acknowledgedAt;
    private String resolutionNotes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructors
    public AlertDto() {}
    
    public AlertDto(Alert alert) {
        this.id = alert.getId();
        this.repositoryId = alert.getRepository().getId();
        this.repositoryName = alert.getRepository().getName();
        this.repositoryFullName = alert.getRepository().getFullName();
        this.filePath = alert.getFilePath();
        this.lineNumber = alert.getLineNumber();
        this.commitSha = alert.getCommitSha();
        this.secretType = alert.getSecretType();
        this.severity = alert.getSeverity();
        this.confidenceScore = alert.getConfidenceScore();
        this.partialSecret = alert.getPartialSecret();
        this.contextBefore = alert.getContextBefore();
        this.contextAfter = alert.getContextAfter();
        this.status = alert.getStatus();
        this.acknowledgedAt = alert.getAcknowledgedAt();
        this.resolutionNotes = alert.getResolutionNotes();
        this.createdAt = alert.getCreatedAt();
        this.updatedAt = alert.getUpdatedAt();
        
        if (alert.getAcknowledgedByUser() != null) {
            this.acknowledgedByUsername = alert.getAcknowledgedByUser().getUsername();
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getRepositoryId() {
        return repositoryId;
    }
    
    public void setRepositoryId(Long repositoryId) {
        this.repositoryId = repositoryId;
    }
    
    public String getRepositoryName() {
        return repositoryName;
    }
    
    public void setRepositoryName(String repositoryName) {
        this.repositoryName = repositoryName;
    }
    
    public String getRepositoryFullName() {
        return repositoryFullName;
    }
    
    public void setRepositoryFullName(String repositoryFullName) {
        this.repositoryFullName = repositoryFullName;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public Integer getLineNumber() {
        return lineNumber;
    }
    
    public void setLineNumber(Integer lineNumber) {
        this.lineNumber = lineNumber;
    }
    
    public String getCommitSha() {
        return commitSha;
    }
    
    public void setCommitSha(String commitSha) {
        this.commitSha = commitSha;
    }
    
    public String getSecretType() {
        return secretType;
    }
    
    public void setSecretType(String secretType) {
        this.secretType = secretType;
    }
    
    public Severity getSeverity() {
        return severity;
    }
    
    public void setSeverity(Severity severity) {
        this.severity = severity;
    }
    
    public BigDecimal getConfidenceScore() {
        return confidenceScore;
    }
    
    public void setConfidenceScore(BigDecimal confidenceScore) {
        this.confidenceScore = confidenceScore;
    }
    
    public String getPartialSecret() {
        return partialSecret;
    }
    
    public void setPartialSecret(String partialSecret) {
        this.partialSecret = partialSecret;
    }
    
    public String getContextBefore() {
        return contextBefore;
    }
    
    public void setContextBefore(String contextBefore) {
        this.contextBefore = contextBefore;
    }
    
    public String getContextAfter() {
        return contextAfter;
    }
    
    public void setContextAfter(String contextAfter) {
        this.contextAfter = contextAfter;
    }
    
    public AlertStatus getStatus() {
        return status;
    }
    
    public void setStatus(AlertStatus status) {
        this.status = status;
    }
    
    public String getAcknowledgedByUsername() {
        return acknowledgedByUsername;
    }
    
    public void setAcknowledgedByUsername(String acknowledgedByUsername) {
        this.acknowledgedByUsername = acknowledgedByUsername;
    }
    
    public LocalDateTime getAcknowledgedAt() {
        return acknowledgedAt;
    }
    
    public void setAcknowledgedAt(LocalDateTime acknowledgedAt) {
        this.acknowledgedAt = acknowledgedAt;
    }
    
    public String getResolutionNotes() {
        return resolutionNotes;
    }
    
    public void setResolutionNotes(String resolutionNotes) {
        this.resolutionNotes = resolutionNotes;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
