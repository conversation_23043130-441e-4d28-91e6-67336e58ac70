package com.github.secrets.dto;

import com.github.secrets.entity.SecretPattern;
import com.github.secrets.enums.Severity;

import java.math.BigDecimal;

public class SecretMatch {
    
    private String secretType;
    private Severity severity;
    private String matchedText;
    private String partialSecret;
    private int startIndex;
    private int endIndex;
    private int lineNumber;
    private String contextBefore;
    private String contextAfter;
    private BigDecimal confidenceScore;
    private SecretPattern pattern;
    private String filePath;
    private double entropyScore;
    
    // Constructors
    public SecretMatch() {}
    
    public SecretMatch(SecretPattern pattern, String matchedText, int startIndex, int endIndex, 
                      int lineNumber, String filePath) {
        this.pattern = pattern;
        this.secretType = pattern.getSecretType();
        this.severity = pattern.getSeverity();
        this.matchedText = matchedText;
        this.startIndex = startIndex;
        this.endIndex = endIndex;
        this.lineNumber = lineNumber;
        this.filePath = filePath;
        this.partialSecret = createPartialSecret(matchedText);
    }
    
    // Getters and Setters
    public String getSecretType() {
        return secretType;
    }
    
    public void setSecretType(String secretType) {
        this.secretType = secretType;
    }
    
    public Severity getSeverity() {
        return severity;
    }
    
    public void setSeverity(Severity severity) {
        this.severity = severity;
    }
    
    public String getMatchedText() {
        return matchedText;
    }
    
    public void setMatchedText(String matchedText) {
        this.matchedText = matchedText;
        this.partialSecret = createPartialSecret(matchedText);
    }
    
    public String getPartialSecret() {
        return partialSecret;
    }
    
    public void setPartialSecret(String partialSecret) {
        this.partialSecret = partialSecret;
    }
    
    public int getStartIndex() {
        return startIndex;
    }
    
    public void setStartIndex(int startIndex) {
        this.startIndex = startIndex;
    }
    
    public int getEndIndex() {
        return endIndex;
    }
    
    public void setEndIndex(int endIndex) {
        this.endIndex = endIndex;
    }
    
    public int getLineNumber() {
        return lineNumber;
    }
    
    public void setLineNumber(int lineNumber) {
        this.lineNumber = lineNumber;
    }
    
    public String getContextBefore() {
        return contextBefore;
    }
    
    public void setContextBefore(String contextBefore) {
        this.contextBefore = contextBefore;
    }
    
    public String getContextAfter() {
        return contextAfter;
    }
    
    public void setContextAfter(String contextAfter) {
        this.contextAfter = contextAfter;
    }
    
    public BigDecimal getConfidenceScore() {
        return confidenceScore;
    }
    
    public void setConfidenceScore(BigDecimal confidenceScore) {
        this.confidenceScore = confidenceScore;
    }
    
    public SecretPattern getPattern() {
        return pattern;
    }
    
    public void setPattern(SecretPattern pattern) {
        this.pattern = pattern;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public double getEntropyScore() {
        return entropyScore;
    }
    
    public void setEntropyScore(double entropyScore) {
        this.entropyScore = entropyScore;
    }
    
    private String createPartialSecret(String secret) {
        if (secret == null || secret.length() <= 8) {
            return secret != null ? "*".repeat(secret.length()) : "";
        }
        
        String start = secret.substring(0, 4);
        String end = secret.substring(secret.length() - 4);
        String middle = "*".repeat(Math.max(0, secret.length() - 8));
        
        return start + middle + end;
    }
    
    @Override
    public String toString() {
        return "SecretMatch{" +
                "secretType='" + secretType + '\'' +
                ", severity=" + severity +
                ", lineNumber=" + lineNumber +
                ", filePath='" + filePath + '\'' +
                ", confidenceScore=" + confidenceScore +
                ", entropyScore=" + entropyScore +
                '}';
    }
}
