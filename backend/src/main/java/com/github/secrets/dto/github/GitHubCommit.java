package com.github.secrets.dto.github;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GitHubCommit {
    
    private String sha;
    private GitHubCommitDetails commit;
    private GitHubAuthor author;
    private GitHubAuthor committer;
    
    @JsonProperty("html_url")
    private String htmlUrl;
    
    // Constructors
    public GitHubCommit() {}
    
    // Getters and Setters
    public String getSha() {
        return sha;
    }
    
    public void setSha(String sha) {
        this.sha = sha;
    }
    
    public GitHubCommitDetails getCommit() {
        return commit;
    }
    
    public void setCommit(GitHubCommitDetails commit) {
        this.commit = commit;
    }
    
    public GitHubAuthor getAuthor() {
        return author;
    }
    
    public void setAuthor(GitHubAuthor author) {
        this.author = author;
    }
    
    public GitHubAuthor getCommitter() {
        return committer;
    }
    
    public void setCommitter(GitHubAuthor committer) {
        this.committer = committer;
    }
    
    public String getHtmlUrl() {
        return htmlUrl;
    }
    
    public void setHtmlUrl(String htmlUrl) {
        this.htmlUrl = htmlUrl;
    }
    
    @Override
    public String toString() {
        return "GitHubCommit{" +
                "sha='" + sha + '\'' +
                ", message='" + (commit != null ? commit.getMessage() : null) + '\'' +
                '}';
    }
    
    public static class GitHubCommitDetails {
        private String message;
        private GitHubCommitAuthor author;
        private GitHubCommitAuthor committer;
        
        // Getters and Setters
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public GitHubCommitAuthor getAuthor() {
            return author;
        }
        
        public void setAuthor(GitHubCommitAuthor author) {
            this.author = author;
        }
        
        public GitHubCommitAuthor getCommitter() {
            return committer;
        }
        
        public void setCommitter(GitHubCommitAuthor committer) {
            this.committer = committer;
        }
    }
    
    public static class GitHubCommitAuthor {
        private String name;
        private String email;
        private String date;
        
        // Getters and Setters
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getEmail() {
            return email;
        }
        
        public void setEmail(String email) {
            this.email = email;
        }
        
        public String getDate() {
            return date;
        }
        
        public void setDate(String date) {
            this.date = date;
        }
    }
    
    public static class GitHubAuthor {
        private Long id;
        private String login;
        
        @JsonProperty("avatar_url")
        private String avatarUrl;
        
        // Getters and Setters
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public String getLogin() {
            return login;
        }
        
        public void setLogin(String login) {
            this.login = login;
        }
        
        public String getAvatarUrl() {
            return avatarUrl;
        }
        
        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }
    }
}
