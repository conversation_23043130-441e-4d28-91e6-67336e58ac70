package com.github.secrets.dto.github;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GitHubContent {
    
    private String name;
    private String path;
    private String sha;
    private Long size;
    private String type; // file, dir
    
    @JsonProperty("download_url")
    private String downloadUrl;
    
    @JsonProperty("git_url")
    private String gitUrl;
    
    @JsonProperty("html_url")
    private String htmlUrl;
    
    private String content; // Base64 encoded content
    private String encoding; // base64
    
    // Constructors
    public GitHubContent() {}
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getSha() {
        return sha;
    }
    
    public void setSha(String sha) {
        this.sha = sha;
    }
    
    public Long getSize() {
        return size;
    }
    
    public void setSize(Long size) {
        this.size = size;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getDownloadUrl() {
        return downloadUrl;
    }
    
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }
    
    public String getGitUrl() {
        return gitUrl;
    }
    
    public void setGitUrl(String gitUrl) {
        this.gitUrl = gitUrl;
    }
    
    public String getHtmlUrl() {
        return htmlUrl;
    }
    
    public void setHtmlUrl(String htmlUrl) {
        this.htmlUrl = htmlUrl;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getEncoding() {
        return encoding;
    }
    
    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }
    
    public boolean isFile() {
        return "file".equals(type);
    }
    
    public boolean isDirectory() {
        return "dir".equals(type);
    }
    
    @Override
    public String toString() {
        return "GitHubContent{" +
                "name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", type='" + type + '\'' +
                ", size=" + size +
                '}';
    }
}
