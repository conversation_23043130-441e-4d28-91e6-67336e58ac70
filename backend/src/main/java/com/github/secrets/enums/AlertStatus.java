package com.github.secrets.enums;

public enum AlertStatus {
    OPEN("Open"),
    ACKNOWLEDGED("Acknowledged"),
    RESOLVED("Resolved"),
    FALSE_POSITIVE("False Positive");
    
    private final String displayName;
    
    AlertStatus(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
