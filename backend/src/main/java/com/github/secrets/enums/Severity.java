package com.github.secrets.enums;

public enum Severity {
    LOW("Low"),
    MEDIUM("Medium"),
    HIGH("High");
    
    private final String displayName;
    
    Severity(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
