-- Users table for authentication
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    is_active B<PERSON><PERSON>EAN DEFAULT true,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- GitHub repositories being monitored
CREATE TABLE repositories (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE, -- owner/repo-name
    owner <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    github_id BIGINT NOT NULL UNIQUE,
    clone_url VARCHAR(500) NOT NULL,
    default_branch VARCHAR(255) DEFAULT 'main',
    is_private BOOLEAN DEFAULT false,
    is_active B<PERSON><PERSON><PERSON><PERSON> DEFAULT true,
    last_scan_at TIMESTAMP,
    last_commit_sha VARCHAR(40),
    scan_frequency_minutes INTEGER DEFAULT 15,
    added_by_user_id BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Secret detection patterns and rules
CREATE TABLE secret_patterns (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    pattern_regex TEXT NOT NULL,
    secret_type VARCHAR(100) NOT NULL, -- AWS_ACCESS_KEY, GITHUB_TOKEN, etc.
    severity VARCHAR(20) DEFAULT 'MEDIUM', -- HIGH, MEDIUM, LOW
    is_active BOOLEAN DEFAULT true,
    entropy_threshold DECIMAL(3,2), -- For entropy-based detection
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Scan jobs and their status
CREATE TABLE scan_jobs (
    id BIGSERIAL PRIMARY KEY,
    repository_id BIGINT NOT NULL REFERENCES repositories(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, RUNNING, COMPLETED, FAILED
    scan_type VARCHAR(50) DEFAULT 'SCHEDULED', -- SCHEDULED, MANUAL, WEBHOOK
    commit_sha VARCHAR(40),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    files_scanned INTEGER DEFAULT 0,
    secrets_found INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Detected secrets/alerts
CREATE TABLE alerts (
    id BIGSERIAL PRIMARY KEY,
    repository_id BIGINT NOT NULL REFERENCES repositories(id) ON DELETE CASCADE,
    scan_job_id BIGINT REFERENCES scan_jobs(id) ON DELETE SET NULL,
    secret_pattern_id BIGINT REFERENCES secret_patterns(id),
    file_path VARCHAR(1000) NOT NULL,
    line_number INTEGER NOT NULL,
    commit_sha VARCHAR(40) NOT NULL,
    secret_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL, -- 0.00 to 1.00
    partial_secret VARCHAR(20), -- First and last 4 chars for reference
    context_before TEXT, -- Code context before the secret
    context_after TEXT, -- Code context after the secret
    status VARCHAR(50) DEFAULT 'OPEN', -- OPEN, ACKNOWLEDGED, RESOLVED, FALSE_POSITIVE
    acknowledged_by_user_id BIGINT REFERENCES users(id),
    acknowledged_at TIMESTAMP,
    resolution_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User settings and preferences
CREATE TABLE user_settings (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    setting_key VARCHAR(255) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, setting_key)
);

-- GitHub API tokens (encrypted)
CREATE TABLE github_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_name VARCHAR(255) NOT NULL,
    encrypted_token TEXT NOT NULL,
    scopes TEXT[], -- Array of GitHub scopes
    rate_limit_remaining INTEGER,
    rate_limit_reset_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notification settings
CREATE TABLE notification_settings (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    notification_type VARCHAR(100) NOT NULL, -- EMAIL, WEBHOOK, SLACK
    is_enabled BOOLEAN DEFAULT true,
    configuration JSONB, -- Store type-specific config (email addresses, webhook URLs, etc.)
    severity_filter VARCHAR(20)[], -- Array of severities to notify for
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Audit log for tracking changes
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100) NOT NULL, -- REPOSITORY, ALERT, USER, etc.
    resource_id BIGINT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_repositories_owner ON repositories(owner);
CREATE INDEX idx_repositories_active ON repositories(is_active);
CREATE INDEX idx_repositories_last_scan ON repositories(last_scan_at);

CREATE INDEX idx_alerts_repository_id ON alerts(repository_id);
CREATE INDEX idx_alerts_status ON alerts(status);
CREATE INDEX idx_alerts_severity ON alerts(severity);
CREATE INDEX idx_alerts_created_at ON alerts(created_at);
CREATE INDEX idx_alerts_commit_sha ON alerts(commit_sha);

CREATE INDEX idx_scan_jobs_repository_id ON scan_jobs(repository_id);
CREATE INDEX idx_scan_jobs_status ON scan_jobs(status);
CREATE INDEX idx_scan_jobs_created_at ON scan_jobs(created_at);

CREATE INDEX idx_secret_patterns_type ON secret_patterns(secret_type);
CREATE INDEX idx_secret_patterns_active ON secret_patterns(is_active);

CREATE INDEX idx_github_tokens_user_id ON github_tokens(user_id);
CREATE INDEX idx_github_tokens_active ON github_tokens(is_active);

CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);

-- Insert default secret patterns
INSERT INTO secret_patterns (name, description, pattern_regex, secret_type, severity, entropy_threshold) VALUES
('AWS Access Key ID', 'AWS Access Key ID starting with AKIA or ASIA', '(AKIA|ASIA)[0-9A-Z]{16}', 'AWS_ACCESS_KEY', 'HIGH', 0.6),
('AWS Secret Access Key', 'AWS Secret Access Key (40 character base64)', '[A-Za-z0-9/+=]{40}', 'AWS_SECRET_KEY', 'HIGH', 0.8),
('GitHub Personal Access Token', 'GitHub personal access token', 'ghp_[A-Za-z0-9]{36}', 'GITHUB_TOKEN', 'HIGH', 0.7),
('GitHub OAuth Token', 'GitHub OAuth access token', 'gho_[A-Za-z0-9]{36}', 'GITHUB_TOKEN', 'HIGH', 0.7),
('GitHub User Token', 'GitHub user-to-server token', 'ghu_[A-Za-z0-9]{36}', 'GITHUB_TOKEN', 'HIGH', 0.7),
('GitHub App Token', 'GitHub app token', 'github_pat_[A-Za-z0-9_]{82}', 'GITHUB_TOKEN', 'HIGH', 0.7),
('Google API Key', 'Google API key', 'AIza[0-9A-Za-z_-]{35}', 'GOOGLE_API_KEY', 'HIGH', 0.6),
('Generic API Key', 'Generic API key pattern', '(?i)(api[_-]?key|apikey)["\''\\s]*[:=]["\''\\s]*[A-Za-z0-9_-]{16,}', 'API_KEY', 'MEDIUM', 0.6),
('Generic Secret', 'Generic secret pattern', '(?i)(secret|password|passwd|pwd)["\''\\s]*[:=]["\''\\s]*[A-Za-z0-9_-]{8,}', 'SECRET', 'MEDIUM', 0.6),
('JWT Token', 'JSON Web Token', 'eyJ[A-Za-z0-9_-]*\\.eyJ[A-Za-z0-9_-]*\\.[A-Za-z0-9_-]*', 'JWT_TOKEN', 'MEDIUM', 0.7),
('Private Key', 'Private key in PEM format', '-----BEGIN [A-Z ]*PRIVATE KEY-----', 'PRIVATE_KEY', 'HIGH', 0.5),
('Database URL with credentials', 'Database connection string with embedded credentials', '(?i)(jdbc|mongodb|mysql|postgresql)://[^\\s:]+:[^\\s@]+@[^\\s/]+', 'DATABASE_URL', 'HIGH', 0.6);
