body {
  margin: 0;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

.MuiDataGrid-root {
  border: none !important;
}

.MuiDataGrid-cell:focus {
  outline: none !important;
}

.MuiDataGrid-row:hover {
  background-color: rgba(25, 118, 210, 0.04) !important;
}

.severity-high {
  color: #d32f2f !important;
  font-weight: 600;
}

.severity-medium {
  color: #ed6c02 !important;
  font-weight: 600;
}

.severity-low {
  color: #2e7d32 !important;
  font-weight: 600;
}

.secret-type-chip {
  font-size: 0.75rem;
  height: 24px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-message {
  color: #d32f2f;
  text-align: center;
  padding: 20px;
}

.no-data-message {
  text-align: center;
  padding: 40px;
  color: #666;
}

.code-snippet {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  overflow-x: auto;
  margin: 8px 0;
}

.highlighted-secret {
  background-color: #ffeb3b;
  padding: 2px 4px;
  border-radius: 2px;
}
