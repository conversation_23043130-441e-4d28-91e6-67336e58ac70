# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Backend (Java/Maven)
backend/target/
backend/.mvn/wrapper/maven-wrapper.jar
backend/.mvn/wrapper/maven-wrapper.properties
backend/mvnw
backend/mvnw.cmd
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Frontend (React/Node.js)
frontend/node_modules/
frontend/build/
frontend/dist/
frontend/.pnp
frontend/.pnp.js
frontend/coverage/

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Database
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Secrets and sensitive data
secrets/
*.pem
*.key
*.crt
